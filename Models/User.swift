//
//  User.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import Foundation
import SwiftData

@Model
class User: Identifiable {
    @Attribute(.unique) var id: Int
    var username: String
    var email: String?
    var avatarUrl: String?
    var token: String?
    var isLoggedIn: Bool
    var createdAt: Date
    var updatedAt: Date

    init(id: Int, username: String, email: String? = nil, avatarUrl: String? = nil, token: String? = nil) {
        self.id = id
        self.username = username
        self.email = email
        self.avatarUrl = avatarUrl
        self.token = token
        self.isLoggedIn = token != nil
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - User API Response Model
struct UserResponse: Codable {
    let userId: Int
    let username: String
    let email: String?
    let avatarUrl: String?
    let token: String?
    
    enum CodingKeys: String, CodingKey {
        case userId = "user_id"
        case username
        case email
        case avatarUrl = "avatar_url"
        case token
    }
}

// MARK: - User Factory
extension User {
    static func from(_ response: UserResponse) -> User {
        return User(
            id: response.userId,
            username: response.username,
            email: response.email,
            avatarUrl: response.avatarUrl,
            token: response.token
        )
    }
}
