//
//  Course.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import Foundation
import SwiftData

@Model
class Course: ObservableObject, Identifiable {
    @Attribute(.unique) var id: Int
    var courseName: String
    var englishName: String?
    var description: String?
    var tag: String?
    var subtitle: String?
    var createdAt: Date
    var updatedAt: Date
    
    init(id: Int, courseName: String, englishName: String? = nil, description: String? = nil, tag: String? = nil, subtitle: String? = nil) {
        self.id = id
        self.courseName = courseName
        self.englishName = englishName
        self.description = description
        self.tag = tag
        self.subtitle = subtitle
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Course API Response Model
struct CourseResponse: Codable {
    let courseId: Int
    let courseName: String
    let englishName: String?
    let description: String?
    let tag: String?
    let subtitle: String?
    
    enum CodingKeys: String, CodingKey {
        case courseId = "course_id"
        case courseName = "course_name"
        case englishName = "english_name"
        case description
        case tag
        case subtitle
    }
}

// MARK: - Course Extensions
extension Course {
    static func from(_ response: CourseResponse) -> Course {
        return Course(
            id: response.courseId,
            courseName: response.courseName,
            englishName: response.englishName,
            description: response.description,
            tag: response.tag,
            subtitle: response.subtitle
        )
    }
    
    var displayTag: String {
        return tag?.uppercased() ?? "LEARNING PROGRESS"
    }
    
    var displaySubtitle: String {
        return subtitle ?? "WakeUp Skill Boosting"
    }
    
    var displayEnglishName: String {
        return englishName ?? "Learning course \(id)"
    }
}
