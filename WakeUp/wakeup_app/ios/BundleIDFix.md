# Bundle ID配置说明

## 当前Bundle ID
`com.navoris.wakeupApp`

## 问题原因
1. Bundle ID可能未在Apple Developer Console中注册
2. 或者未启用Sign in with Apple服务
3. 或者使用的是免费Apple Developer账户

## 解决方案

### 方案1：在Apple Developer Console中配置
1. 访问 https://developer.apple.com
2. 登录您的Apple Developer账户
3. 进入 "Certificates, Identifiers & Profiles"
4. 选择 "Identifiers"
5. 找到或创建 `com.navoris.wakeupApp`
6. 启用 "Sign in with Apple" 能力

### 方案2：使用临时Bundle ID（仅用于测试）
如果您想快速测试Apple登录功能，可以：

1. 在Xcode中修改Bundle ID为：`com.example.testapp.$(RANDOM_UUID)`
2. 这样可以临时测试Apple登录功能
3. 但正式发布时需要使用正确的Bundle ID

### 方案3：手动添加配置（推荐）
我已经为您创建了 `Runner.entitlements` 文件，其中包含了Apple登录的配置。

## 验证配置
创建entitlements文件后，请：
1. 重新构建项目：`flutter clean && flutter run`
2. 测试Apple登录功能
3. 查看调试信息中是否还显示错误代码1000

## 如果问题持续存在
可能需要：
1. 升级到付费的Apple Developer账户
2. 在Apple Developer Console中正确配置Bundle ID
3. 或联系Apple Developer支持