#!/bin/bash

echo "开始修正iOS应用图标尺寸..."

ICON_SET="Runner/Assets.xcassets/AppIcon.appiconset"
SOURCE_ICON="$ICON_SET/<EMAIL>" 
SOURCE_DARK_ICON="$ICON_SET/<EMAIL>"

# 50x50 图标 (1x)
echo "调整 50x50@1x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 50 50 "$ICON_SET/<EMAIL>" &>/dev/null

# 50x50 图标 (2x)
echo "调整 50x50@2x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 100 100 "$ICON_SET/<EMAIL>" &>/dev/null

# 72x72 图标 (1x)
echo "调整 72x72@1x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 72 72 "$ICON_SET/<EMAIL>" &>/dev/null

# 72x72 图标 (2x)
echo "调整 72x72@2x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 144 144 "$ICON_SET/<EMAIL>" &>/dev/null

# 57x57 图标 (1x)
echo "调整 57x57@1x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 57 57 "$ICON_SET/<EMAIL>" &>/dev/null

# 57x57 图标 (2x)
echo "调整 57x57@2x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 114 114 "$ICON_SET/<EMAIL>" &>/dev/null

# 深色模式图标
echo "调整深色模式图标..."

# 50x50 暗色图标 (1x)
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 50 50 "$ICON_SET/<EMAIL>" &>/dev/null

# 50x50 暗色图标 (2x)
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 100 100 "$ICON_SET/<EMAIL>" &>/dev/null

# 72x72 暗色图标 (1x)
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 72 72 "$ICON_SET/<EMAIL>" &>/dev/null

# 72x72 暗色图标 (2x)
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 144 144 "$ICON_SET/<EMAIL>" &>/dev/null

# 57x57 暗色图标 (1x)
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 57 57 "$ICON_SET/<EMAIL>" &>/dev/null

# 57x57 暗色图标 (2x)
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 114 114 "$ICON_SET/<EMAIL>" &>/dev/null

echo "所有图标已调整为正确尺寸!" 