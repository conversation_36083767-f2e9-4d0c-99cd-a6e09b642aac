# Apple登录配置指南

## 问题现象
Apple登录按钮点击后没有弹出原生登录窗口

## 可能原因和解决方案

### 1. 🔧 在Xcode中启用Sign in with Apple能力

**这是最常见的问题！**

1. 打开Xcode：
   ```bash
   open ios/Runner.xcworkspace
   ```

2. 在Xcode中：
   - 选择左侧的 `Runner` 项目
   - 点击 `Runner` target
   - 选择 `Signing & Capabilities` 选项卡
   - 点击 `+ Capability`
   - 搜索并添加 `Sign in with Apple`

3. 确保开发者账户配置：
   - 确保使用了有效的Apple开发者账户
   - Bundle ID需要在Apple Developer Console中注册
   - 启用了App ID的Sign in with Apple服务

### 2. 📱 检查设备和iOS版本

Apple登录要求：
- iOS 13.0 或更高版本
- 真机设备（模拟器可能有限制）
- 设备已登录Apple ID

### 3. 🔍 检查Bundle ID配置

确保Info.plist中的Bundle ID与Apple Developer Console中的一致：
```xml
<key>CFBundleIdentifier</key>
<string>your.bundle.identifier</string>
```

### 4. 🏗️ 重新构建项目

完成配置后：
```bash
flutter clean
flutter pub get
cd ios && pod install
cd .. && flutter run
```

### 5. 📝 调试步骤

在 `_performSocialLogin` 方法中添加调试日志：

```dart
try {
  print('🍎 开始Apple登录...');
  
  // 检查可用性
  final isAvailable = await SignInWithApple.isAvailable();
  print('🍎 Apple登录可用性: $isAvailable');
  
  if (!isAvailable) {
    print('❌ 当前设备不支持Apple登录');
    return;
  }
  
  // 执行登录
  final result = await SocialAuthService.signInWith(SocialProvider.apple);
  print('🍎 Apple登录结果: $result');
  
} catch (e) {
  print('❌ Apple登录错误: $e');
}
```

### 6. 🔧 常见错误处理

- **Error: Not supported** → 需要在Xcode中启用能力
- **Error: Canceled** → 用户取消了登录
- **Error: Failed** → 检查网络连接和Apple服务状态
- **No response** → 检查Bundle ID配置

### 7. ✅ 验证配置

成功配置后，Apple登录应该：
1. 点击按钮后立即弹出原生登录界面
2. 显示Face ID/Touch ID选项（如果设备支持）
3. 可以选择共享或隐藏邮箱信息
4. 登录成功后返回到应用

## 📞 如果问题持续存在

1. 检查Apple Developer Console中的App ID配置
2. 确认证书和配置文件是否正确
3. 尝试在不同的设备上测试
4. 检查Apple系统服务状态