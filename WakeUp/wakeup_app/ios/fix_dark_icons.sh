#!/bin/bash

echo "开始处理深色模式iOS应用图标..."

ICON_SET="Runner/Assets.xcassets/AppIcon.appiconset"
SOURCE_DARK_ICON="$ICON_SET/<EMAIL>"

# 确保所有必要的深色模式图标存在
# iPhone 图标
echo "创建所有深色模式iPhone图标..."
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 40 40 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 60 60 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 29 29 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 58 58 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 87 87 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 80 80 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 120 120 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 120 120 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 180 180 "$ICON_SET/<EMAIL>" &>/dev/null

# iPad 图标
echo "创建所有深色模式iPad图标..."
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 20 20 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 40 40 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 76 76 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 152 152 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 167 167 "$ICON_SET/<EMAIL>" &>/dev/null

# 旧版iOS图标 (iOS 6.1及更早版本)
echo "创建旧版iOS深色模式图标..."
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 57 57 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 114 114 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 50 50 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 100 100 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 72 72 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 144 144 "$ICON_SET/<EMAIL>" &>/dev/null

echo "所有深色模式图标已创建并调整为正确尺寸！" 