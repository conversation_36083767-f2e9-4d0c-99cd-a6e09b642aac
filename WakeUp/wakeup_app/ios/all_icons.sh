#!/bin/bash

echo "开始全面修复iOS应用图标..."

ICON_SET="Runner/Assets.xcassets/AppIcon.appiconset"
SOURCE_ICON="$ICON_SET/<EMAIL>" 
SOURCE_DARK_ICON="$ICON_SET/<EMAIL>"

# 第一部分：修复所有标准图标
echo "===== 修复标准图标 ====="

# 标准图标 (57x57)
echo "调整 57x57@1x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 57 57 "$ICON_SET/<EMAIL>" &>/dev/null

echo "调整 57x57@2x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 114 114 "$ICON_SET/<EMAIL>" &>/dev/null

# 旧iPad图标 (50x50)
echo "调整 50x50@1x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 50 50 "$ICON_SET/<EMAIL>" &>/dev/null

echo "调整 50x50@2x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 100 100 "$ICON_SET/<EMAIL>" &>/dev/null

# 旧iPad图标 (72x72)
echo "调整 72x72@1x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 72 72 "$ICON_SET/<EMAIL>" &>/dev/null

echo "调整 72x72@2x 图标..."
cp "$SOURCE_ICON" "$ICON_SET/<EMAIL>"
sips -z 144 144 "$ICON_SET/<EMAIL>" &>/dev/null

# 第二部分：处理所有深色模式图标
echo "===== 处理深色模式图标 ====="

# iPhone 图标
echo "创建深色模式iPhone图标..."
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 40 40 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 60 60 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 29 29 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 58 58 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 87 87 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 80 80 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 120 120 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 120 120 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 180 180 "$ICON_SET/<EMAIL>" &>/dev/null

# iPad 图标
echo "创建深色模式iPad图标..."
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 20 20 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 40 40 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 76 76 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 152 152 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 167 167 "$ICON_SET/<EMAIL>" &>/dev/null

# 深色模式 - 旧版iOS图标 (iOS 6.1及更早版本)
echo "创建深色模式旧版iOS图标..."
cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 57 57 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 114 114 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 50 50 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 100 100 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 72 72 "$ICON_SET/<EMAIL>" &>/dev/null

cp "$SOURCE_DARK_ICON" "$ICON_SET/<EMAIL>"
sips -z 144 144 "$ICON_SET/<EMAIL>" &>/dev/null

echo "✅ 所有图标已成功调整为正确尺寸！"
echo "🔍 现在可以在Xcode中打开项目并验证图标集是否完整。" 