#!/usr/bin/env bash
# Git pre-commit hook: block committing debugPrint/print in Dart code
# Usage (once per repo):
#   git config core.hooksPath .githooks
#   chmod +x .githooks/pre-commit

set -euo pipefail

# Colored output helpers
RED="\033[31m"
YELLOW="\033[33m"
GREEN="\033[32m"
RESET="\033[0m"

# Collect staged Dart files (Added, Copied, Modified)
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.dart$' || true)

if [ -z "$STAGED_FILES" ]; then
  exit 0
fi

VIOLATION_COUNT=0

# Regex to catch debugPrint( or print( even with spaces: matches non-word boundary before symbol
PATTERN='(^|[^A-Za-z0-9_])(debugPrint|print)[[:space:]]*\('

for FILE in $STAGED_FILES; do
  # Allow debugPrint inside the centralized logger implementation
  if [[ "$FILE" == *"lib/core/utils/logger.dart"* ]]; then
    continue
  fi

  # Read the staged version of the file
  if ! CONTENT=$(git show ":$FILE" 2>/dev/null); then
    # Fallback to working tree if staged blob is unavailable
    CONTENT=$(cat "$FILE" 2>/dev/null || true)
  fi

  if echo "$CONTENT" | grep -nE "$PATTERN" > /tmp/hook_matches_$$.txt; then
    if [ $VIOLATION_COUNT -eq 0 ]; then
      echo -e "${RED}❌ 拦截提交：检测到不允许的日志 API（debugPrint/print）${RESET}"
      echo -e "${YELLOW}请改用 AppLogger（info/warning/error/success/performance/preload）${RESET}\n"
    fi
    VIOLATION_COUNT=$((VIOLATION_COUNT+1))

    echo -e "${RED}文件:${RESET} $FILE"
    # Show up to 10 matches with line numbers
    head -n 10 /tmp/hook_matches_$$.txt | sed 's/^/  行: /'
    echo
  fi
  rm -f /tmp/hook_matches_$$.txt || true

done

if [ $VIOLATION_COUNT -gt 0 ]; then
  echo -e "${YELLOW}提示:${RESET} 如需临时绕过（不建议），可使用: git commit --no-verify\n"
  exit 1
fi

# Optional success message (kept quiet)
# echo -e "${GREEN}✔ 无违规日志，允许提交${RESET}"

exit 0

