import 'package:flutter/material.dart';
// import 'package:cached_network_image/cached_network_image.dart'; // 需要添加到 pubspec.yaml

/// 优化的图片组件，支持多级缓存和内存管理
class OptimizedImage extends StatelessWidget {
  final String? imageUrl;
  final String? placeholder;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration cacheMaxAge;

  const OptimizedImage({
    super.key,
    this.imageUrl,
    this.placeholder,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.cacheMaxAge = const Duration(days: 7),
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildPlaceholder();
    }

    // 网络图片使用 Image.network (可以后续替换为 CachedNetworkImage)
    if (imageUrl!.startsWith('http')) {
      return Image.network(
        imageUrl!,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: _getOptimalCacheWidth(),
        cacheHeight: _getOptimalCacheHeight(),
        loadingBuilder:
            (context, child, loadingProgress) =>
                loadingProgress == null ? child : _buildPlaceholder(),
        errorBuilder:
            (context, error, stackTrace) => errorWidget ?? _buildErrorWidget(),
      );
    }

    // 本地资源图片
    return Image.asset(
      imageUrl!,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: _getOptimalCacheWidth(),
      cacheHeight: _getOptimalCacheHeight(),
      errorBuilder:
          (context, error, stackTrace) => errorWidget ?? _buildErrorWidget(),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child:
          placeholder != null
              ? Image.asset(placeholder!, fit: fit)
              : const Center(
                child: Icon(Icons.image_outlined, color: Colors.grey),
              ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(Icons.broken_image_outlined, color: Colors.grey),
      ),
    );
  }

  /// 根据显示尺寸计算最优缓存宽度
  int? _getOptimalCacheWidth() {
    if (width == null) return null;
    // 考虑设备像素比，但限制最大缓存尺寸
    final pixelRatio =
        WidgetsBinding
            .instance
            .platformDispatcher
            .implicitView
            ?.devicePixelRatio ??
        1.0;
    return (width! * pixelRatio).round().clamp(0, 1080);
  }

  /// 根据显示尺寸计算最优缓存高度
  int? _getOptimalCacheHeight() {
    if (height == null) return null;
    final pixelRatio =
        WidgetsBinding
            .instance
            .platformDispatcher
            .implicitView
            ?.devicePixelRatio ??
        1.0;
    return (height! * pixelRatio).round().clamp(0, 1920);
  }
}

/// 图片内存管理器
class ImageMemoryManager {
  static final ImageMemoryManager _instance = ImageMemoryManager._internal();
  factory ImageMemoryManager() => _instance;
  ImageMemoryManager._internal();

  /// 清理图片缓存
  static void clearImageCache() {
    imageCache.clear();
    imageCache.clearLiveImages();
  }

  /// 设置图片缓存大小限制
  static void configureImageCache({
    int maxCacheImages = 1000,
    int maxCacheSize = 100 * 1024 * 1024, // 100MB
  }) {
    imageCache.maximumSize = maxCacheImages;
    imageCache.maximumSizeBytes = maxCacheSize;
  }

  /// 当内存警告时清理缓存
  static void onMemoryWarning() {
    debugPrint('🧹 [ImageCache] 收到内存警告，清理图片缓存');
    imageCache.clear();
  }
}
