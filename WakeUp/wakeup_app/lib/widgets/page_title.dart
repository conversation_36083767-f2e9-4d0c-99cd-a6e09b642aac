import 'package:flutter/material.dart';

/// 页面标题组件
///
/// 统一样式的页面标题文字，使用Roboto-Bold字体，32px大小，纯白色
class PageTitle extends StatelessWidget {
  /// 标题文本
  final String text;

  /// 可选的左边距，默认为24.0
  final double leftPadding;

  /// 可选的右边距，默认为24.0
  final double rightPadding;

  /// 可选的顶部边距，默认为16.0
  final double topPadding;

  /// 可选的底部边距，默认为16.0
  final double bottomPadding;

  const PageTitle({
    super.key,
    required this.text,
    this.leftPadding = 24.0,
    this.rightPadding = 24.0,
    this.topPadding = 16.0,
    this.bottomPadding = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: leftPadding,
        right: rightPadding,
        top: topPadding,
        bottom: bottomPadding,
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontFamily: 'Roboto',
          fontWeight: FontWeight.bold,
          fontSize: 32,
          color: Colors.white,
        ),
      ),
    );
  }
}
