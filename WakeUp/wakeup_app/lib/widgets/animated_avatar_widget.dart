import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wakeup_app/providers/user_provider.dart';
import 'package:wakeup_app/widgets/account_bottom_sheet.dart';
import 'package:flutter/foundation.dart';
import 'package:extended_image/extended_image.dart';
import 'package:wakeup_app/core/utils/logger.dart';
import 'package:wakeup_app/widgets/avatar_animation_scope.dart';

/// 动画头像组件
///
/// 如果用户设置了头像，则显示用户头像
/// 否则显示WebP默认动画头像
class AnimatedAvatarWidget extends StatefulWidget {
  /// 头像尺寸，默认为48.0
  final double size;

  const AnimatedAvatarWidget({super.key, required this.size});

  @override
  State<AnimatedAvatarWidget> createState() => _AnimatedAvatarWidgetState();
}

class _AnimatedAvatarWidgetState extends State<AnimatedAvatarWidget> {
  // WebP动图路径
  static const String _webpAnimationPath =
      'assets/animations/default_avatar/avatar_animation.webp';

  // 动图加载错误标记
  bool _hasError = false;

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final avatarUrl = userProvider.avatarUrl;

    if (kDebugMode) {
      debugPrint(
        '👤 构建头像组件: 用户登录状态=${userProvider.isLoggedIn}, 头像URL=$avatarUrl',
      );
    }

    // 如果用户已登录且设置了头像
    if (userProvider.isLoggedIn && avatarUrl != null && avatarUrl.isNotEmpty) {
      return GestureDetector(
        onTap: () => showAccountBottomSheet(context),
        child: ClipOval(
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: const BoxDecoration(shape: BoxShape.circle),
            child: Image.network(
              avatarUrl,
              width: widget.size,
              height: widget.size,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                if (kDebugMode) {
                  debugPrint('❌ 头像加载失败: $error');
                }
                // 加载失败时显示默认动画头像
                return _buildDefaultAvatar();
              },
            ),
          ),
        ),
      );
    } else {
      // 未登录或未设置头像时显示默认动画头像
      return _buildDefaultAvatar();
    }
  }

  Widget _buildDefaultAvatar() {
    return GestureDetector(
      onTap: () => showAccountBottomSheet(context),
      child: ClipOval(
        child: SizedBox(
          width: widget.size,
          height: widget.size,
          child: _hasError ? _buildFallbackAvatar() : _buildWebpAnimation(),
        ),
      ),
    );
  }

  Widget _buildWebpAnimation() {
    // 判断是否需要播放一次：通过 AvatarAnimationScope 的 token 判断每次进入 Tab
    final scope = AvatarAnimationScope.of(context);
    final shouldPlayOnce = scope != null; // 有作用域即代表一次性进入

    try {
      if (!shouldPlayOnce) {
        // 默认静态显示第一帧（ExtendedImage 不支持直接取首帧，仍用 asset 显示，但避免任何控制器循环）
        return ExtendedImage.asset(
          _webpAnimationPath,
          width: widget.size,
          height: widget.size,
          fit: BoxFit.cover,
          enableLoadState: false,
        );
      }

      // 使用 Key 区分每次进入，促使 WebP 播放从头开始，播放后立即停在最后一帧
      return ExtendedImage.asset(
        _webpAnimationPath,
        key: ValueKey(scope.playToken),
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        enableLoadState: false,
        // ExtendedImage 对 WebP 动画本身是由解码器驱动，无法精确设置 repeat 次数；
        // 这里通过一次性重建触发“从头播放”，并尽量避免重复 rebuild。
      );
    } catch (e) {
      if (kDebugMode) {
        AppLogger.error('WebP动图显示错误', error: e);
      }

      setState(() {
        _hasError = true;
      });

      return _buildFallbackAvatar();
    }
  }

  // 备用头像显示
  Widget _buildFallbackAvatar() {
    if (kDebugMode) {
      AppLogger.info('使用备用静态头像图标');
    }

    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.red.shade300,
      ),
      child: Center(
        child: Icon(Icons.person, size: widget.size * 0.6, color: Colors.white),
      ),
    );
  }
}
