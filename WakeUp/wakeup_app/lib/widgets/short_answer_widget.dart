import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../services/auth_service.dart';
import '../core/utils/logger.dart';

/// 简答题组件
class ShortAnswerWidget extends StatefulWidget {
  final Question question;
  final String? userAnswer;
  final Function(String?) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const ShortAnswerWidget({
    super.key,
    required this.question,
    this.userAnswer,
    required this.onAnswerChanged,
    required this.interactionState,
  });

  @override
  State<ShortAnswerWidget> createState() => _ShortAnswerWidgetState();
}

class _ShortAnswerWidgetState extends State<ShortAnswerWidget> {
  late TextEditingController _controller;
  bool _showAnswer = false; // ignore: unused_field
  List<Map<String, String>> _templates = [];
  List<String> _keywords = [];
  bool _isLoadingTemplates = false;
  bool _showTemplates = false;
  bool _showKeywords = false;
  String _selectedTemplate = '';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.userAnswer);
    _showAnswer =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    
    // 加载智能模板和关键词
    _loadSmartTemplates();
  }

  @override
  void didUpdateWidget(ShortAnswerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userAnswer != widget.userAnswer) {
      _controller.text = widget.userAnswer ?? '';
    }

    if (oldWidget.interactionState != widget.interactionState) {
      setState(() {
        _showAnswer =
            widget.interactionState !=
            QuestionInteractionState.waitingForSelection;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 加载智能模板
  Future<void> _loadSmartTemplates() async {
    if (widget.interactionState != QuestionInteractionState.waitingForSelection) {
      return;
    }

    setState(() {
      _isLoadingTemplates = true;
    });

    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/smart_hints/short_answer/${widget.question.id}'),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          setState(() {
            _templates = List<Map<String, String>>.from(
              data['templates']?.map((template) => Map<String, String>.from(template)) ?? []
            );
            _showTemplates = _templates.isNotEmpty;
          });
          
          // 同时生成关键词
          _generateKeywords();
        }
      } else {
        _setDefaultTemplates();
      }
    } catch (e) {
      AppLogger.error('加载简答题模板失败', error: e);
      _setDefaultTemplates();
    } finally {
      setState(() {
        _isLoadingTemplates = false;
      });
    }
  }

  /// 设置默认模板
  void _setDefaultTemplates() {
    setState(() {
      _templates = [
        {"label": "因果关系", "template": "因为...，所以..."},
        {"label": "定义解释", "template": "...是指...，它的特点是..."},
        {"label": "步骤说明", "template": "首先...，然后...，最后..."},
        {"label": "优缺点", "template": "优点：...；缺点：..."},
        {"label": "总结归纳", "template": "综上所述，..."},
        {"label": "举例说明", "template": "例如...，这说明了..."}
      ];
      _showTemplates = true;
    });
    
    _generateKeywords();
  }

  /// 生成关键词
  void _generateKeywords() {
    // 基于题目内容生成关键词
    final questionText = widget.question.content.toLowerCase();
    List<String> keywords = [];
    
    // 常用连接词
    keywords.addAll(['因为', '所以', '但是', '然而', '因此', '由于', '由此可见']);
    
    // 常用开头词
    keywords.addAll(['首先', '其次', '最后', '总的来说', '综上所述', '例如']);
    
    // 常用修饰词
    keywords.addAll(['重要的是', '值得注意的是', '需要强调的是', '换句话说']);
    
    // 根据题目类型添加相关词汇
    if (questionText.contains('优') || questionText.contains('缺') || questionText.contains('利') || questionText.contains('弊')) {
      keywords.addAll(['优点', '缺点', '优势', '劣势', '利', '弊']);
    }
    
    if (questionText.contains('原因') || questionText.contains('为什么')) {
      keywords.addAll(['原因', '因素', '导致', '影响', '结果']);
    }
    
    if (questionText.contains('如何') || questionText.contains('怎样')) {
      keywords.addAll(['方法', '步骤', '流程', '过程', '操作']);
    }
    
    setState(() {
      _keywords = keywords.take(12).toList(); // 限制数量
      _showKeywords = true;
    });
  }

  /// 使用模板
  void _useTemplate(Map<String, String> template) {
    setState(() {
      _selectedTemplate = template['template']!;
      _controller.text = template['template']!;
    });
    widget.onAnswerChanged(template['template']!);
    HapticFeedback.selectionClick();
  }

  /// 插入关键词
  void _insertKeyword(String keyword) {
    final currentText = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    
    String newText;
    if (cursorPosition >= 0) {
      // 在光标位置插入关键词
      newText = currentText.substring(0, cursorPosition) + 
                keyword + 
                currentText.substring(cursorPosition);
    } else {
      // 在末尾添加关键词
      newText = currentText.isEmpty ? keyword : '$currentText$keyword';
    }
    
    setState(() {
      _controller.text = newText;
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: cursorPosition >= 0 ? cursorPosition + keyword.length : newText.length),
      );
    });
    
    widget.onAnswerChanged(newText);
    HapticFeedback.selectionClick();
  }

  /// 清空输入
  void _clearInput() {
    setState(() {
      _controller.clear();
      _selectedTemplate = '';
    });
    widget.onAnswerChanged('');
    HapticFeedback.selectionClick();
  }

  /// 开始语音输入（模拟实现）
  void _startVoiceInput() async {
    try {
      HapticFeedback.mediumImpact();
      
      // 模拟语音识别
      await Future.delayed(const Duration(seconds: 2));
      
      const mockResult = "这是语音识别的结果";
      setState(() {
        _controller.text = mockResult;
      });
      widget.onAnswerChanged(mockResult);
      
    } catch (e) {
      AppLogger.error('语音识别失败', error: e);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 确定是否显示正确/错误状态
    final bool isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final correctAnswer = widget.question.answer ?? ''; // ignore: unused_local_variable

    // 简答题通常需要人工评分，所以这里假设系统自动比对的结果
    // 实际应用中可能需要更复杂的逻辑或人工介入
    final bool isCorrect =
        widget.interactionState == QuestionInteractionState.answerCorrect;

    // 样式颜色
    Color borderColor = Colors.white.withValues(alpha: 0.3);
    if (isShowingFeedback) {
      borderColor =
          isCorrect ? Colors.green : Colors.purple.shade300; // 简答题用紫色表示"已回答"
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 工具栏
        if (!isShowingFeedback) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                // 模板按钮
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _showTemplates = !_showTemplates;
                        if (_showTemplates) _showKeywords = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: _showTemplates 
                            ? Colors.blue.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.article_outlined, color: Colors.white, size: 16),
                          SizedBox(width: 6),
                          Text(
                            '回答模板',
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 关键词按钮
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _showKeywords = !_showKeywords;
                        if (_showKeywords) _showTemplates = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: _showKeywords 
                            ? Colors.purple.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.label_outline, color: Colors.white, size: 16),
                          SizedBox(width: 6),
                          Text(
                            '关键词',
                            style: TextStyle(color: Colors.white, fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 语音按钮
                GestureDetector(
                  onTap: _startVoiceInput,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: const Icon(Icons.mic, color: Colors.white, size: 20),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 清空按钮
                if (_controller.text.isNotEmpty)
                  GestureDetector(
                    onTap: _clearInput,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: const Icon(Icons.clear, color: Colors.white, size: 20),
                    ),
                  ),
              ],
            ),
          ),
        ],
        
        // 文本区域
        ClipRRect(
          borderRadius: BorderRadius.circular(24.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(24.0),
                border: Border.all(color: borderColor, width: 2),
              ),
              child: TextField(
                controller: _controller,
                enabled: !isShowingFeedback,
                maxLines: 6, // 多行文本输入
                minLines: 4,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.newline,
                textCapitalization: TextCapitalization.sentences,
                autocorrect: false,
                enableSuggestions: false,
                enableIMEPersonalizedLearning: false,
                onChanged: (value) {
                  widget.onAnswerChanged(value);
                },
                onEditingComplete: () {
                  widget.onAnswerChanged(_controller.text);
                },
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Noto Sans SC',
                  height: 1.4,
                ),
                decoration: InputDecoration(
                  hintText: _selectedTemplate.isNotEmpty 
                      ? '已选择模板，请完善回答内容...'
                      : '请输入你的回答，或使用上方的模板和关键词...',
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                    fontSize: 16,
                    fontFamily: 'Noto Sans SC',
                  ),
                  contentPadding: const EdgeInsets.all(16),
                  border: InputBorder.none,
                ),
              ),
            ),
          ),
        ),
        
        // 回答模板区域
        if (_showTemplates && !isShowingFeedback && _templates.isNotEmpty) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: Colors.blue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.article_outlined,
                      color: Colors.blue,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    const Text(
                      '回答模板',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showTemplates = false;
                        });
                      },
                      child: Icon(
                        Icons.close,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _templates.map((template) {
                    final isSelected = _selectedTemplate == template['template'];
                    return GestureDetector(
                      onTap: () => _useTemplate(template),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          gradient: isSelected
                              ? LinearGradient(
                                  colors: [
                                    Colors.blue.withValues(alpha: 0.5),
                                    Colors.cyan.withValues(alpha: 0.5),
                                  ],
                                )
                              : LinearGradient(
                                  colors: [
                                    Colors.blue.withValues(alpha: 0.2),
                                    Colors.cyan.withValues(alpha: 0.2),
                                  ],
                                ),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSelected 
                                ? Colors.blue 
                                : Colors.white.withValues(alpha: 0.3),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              template['label']!,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.blue.shade100 : Colors.white.withValues(alpha: 0.8),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              template['template']!,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
        
        // 关键词区域
        if (_showKeywords && !isShowingFeedback && _keywords.isNotEmpty) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: Colors.purple.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.label_outline,
                      color: Colors.purple,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    const Text(
                      '常用关键词',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _showKeywords = false;
                        });
                      },
                      child: Icon(
                        Icons.close,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: _keywords.map((keyword) {
                    return GestureDetector(
                      onTap: () => _insertKeyword(keyword),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.purple.withValues(alpha: 0.3),
                              Colors.pink.withValues(alpha: 0.3),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          keyword,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
        
        // 加载状态
        if (_isLoadingTemplates)
          Container(
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  '正在加载智能模板...',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
