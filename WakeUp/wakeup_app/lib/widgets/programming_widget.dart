import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../services/auth_service.dart';
import '../core/utils/logger.dart';

/// 编程题组件
class ProgrammingWidget extends StatefulWidget {
  final Question question;
  final String? userCode;
  final Function(String?) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const ProgrammingWidget({
    super.key,
    required this.question,
    this.userCode,
    required this.onAnswerChanged,
    required this.interactionState,
  });

  @override
  State<ProgrammingWidget> createState() => _ProgrammingWidgetState();
}

class _ProgrammingWidgetState extends State<ProgrammingWidget> {
  late TextEditingController _controller;
  bool _showAnswer = false; // ignore: unused_field
  List<Map<String, String>> _codeSnippets = [];
  bool _isLoadingSnippets = false;
  bool _showSnippets = false;
  bool _showBlockMode = false;
  String _currentLanguage = 'python';
  
  @override
  void initState() {
    super.initState();
    // 初始化代码编辑器，如果有提供编程提示，使用提示作为初始代码；否则使用用户已有的代码或空字符串
    _controller = TextEditingController(
      text: widget.userCode ?? widget.question.programmingPrompt ?? '',
    );
    _showAnswer =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    
    // 加载代码片段
    _loadCodeSnippets();
  }

  @override
  void didUpdateWidget(ProgrammingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userCode != widget.userCode && widget.userCode != null) {
      _controller.text = widget.userCode!;
    }

    if (oldWidget.interactionState != widget.interactionState) {
      setState(() {
        _showAnswer =
            widget.interactionState !=
            QuestionInteractionState.waitingForSelection;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 加载代码片段
  Future<void> _loadCodeSnippets() async {
    if (widget.interactionState != QuestionInteractionState.waitingForSelection) {
      return;
    }

    setState(() {
      _isLoadingSnippets = true;
    });

    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/smart_hints/programming/${widget.question.id}?language=$_currentLanguage'),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          setState(() {
            _codeSnippets = List<Map<String, String>>.from(
              data['snippets']?.map((snippet) => Map<String, String>.from(snippet)) ?? []
            );
            _showSnippets = _codeSnippets.isNotEmpty;
          });
        }
      } else {
        _setDefaultSnippets();
      }
    } catch (e) {
      AppLogger.error('加载代码片段失败', error: e);
      _setDefaultSnippets();
    } finally {
      setState(() {
        _isLoadingSnippets = false;
      });
    }
  }

  /// 设置默认代码片段
  void _setDefaultSnippets() {
    List<Map<String, String>> defaultSnippets = [];
    
    if (_currentLanguage == 'python') {
      defaultSnippets = [
        {"label": "for循环", "code": "for i in range(n):\n    pass"},
        {"label": "if条件", "code": "if condition:\n    pass\nelse:\n    pass"},
        {"label": "函数定义", "code": "def function_name(param):\n    return result"},
        {"label": "列表推导", "code": "[expr for item in iterable if condition]"},
        {"label": "异常处理", "code": "try:\n    pass\nexcept Exception as e:\n    pass"},
        {"label": "字典操作", "code": "dict.get(key, default_value)"},
        {"label": "输入输出", "code": "print(\"Hello World\")\ninput(\"Enter: \")"},
        {"label": "类定义", "code": "class ClassName:\n    def __init__(self):\n        pass"}
      ];
    } else if (_currentLanguage == 'javascript') {
      defaultSnippets = [
        {"label": "for循环", "code": "for (let i = 0; i < n; i++) {\n    // code\n}"},
        {"label": "if条件", "code": "if (condition) {\n    // code\n} else {\n    // code\n}"},
        {"label": "函数定义", "code": "function functionName(param) {\n    return result;\n}"},
        {"label": "箭头函数", "code": "const funcName = (param) => {\n    return result;\n}"},
        {"label": "异步函数", "code": "async function funcName() {\n    await promise;\n}"},
        {"label": "数组方法", "code": "array.map(item => item * 2)"},
        {"label": "控制台输出", "code": "console.log(\"Hello World\");"}
      ];
    } else if (_currentLanguage == 'java') {
      defaultSnippets = [
        {"label": "for循环", "code": "for (int i = 0; i < n; i++) {\n    // code\n}"},
        {"label": "if条件", "code": "if (condition) {\n    // code\n} else {\n    // code\n}"},
        {"label": "方法定义", "code": "public static void methodName(Type param) {\n    // code\n}"},
        {"label": "类定义", "code": "public class ClassName {\n    // fields and methods\n}"},
        {"label": "异常处理", "code": "try {\n    // code\n} catch (Exception e) {\n    // handle\n}"},
        {"label": "输出", "code": "System.out.println(\"Hello World\");"}
      ];
    }
    
    setState(() {
      _codeSnippets = defaultSnippets;
      _showSnippets = true;
    });
  }

  /// 插入代码片段
  void _insertCodeSnippet(Map<String, String> snippet) {
    final currentText = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;
    
    String newText;
    if (cursorPosition >= 0) {
      // 在光标位置插入代码片段
      newText = currentText.substring(0, cursorPosition) + 
                '\n' + snippet['code']! + '\n' + 
                currentText.substring(cursorPosition);
    } else {
      // 在末尾添加代码片段
      newText = currentText.isEmpty 
          ? snippet['code']! 
          : '$currentText\n${snippet['code']!}';
    }
    
    setState(() {
      _controller.text = newText;
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: newText.length),
      );
    });
    
    widget.onAnswerChanged(newText);
    HapticFeedback.selectionClick();
  }

  /// 切换编程语言
  void _changeLanguage(String language) {
    if (language != _currentLanguage) {
      setState(() {
        _currentLanguage = language;
      });
      _loadCodeSnippets();
    }
  }

  /// 清空代码
  void _clearCode() {
    setState(() {
      _controller.clear();
    });
    widget.onAnswerChanged('');
    HapticFeedback.selectionClick();
  }

  /// 自动格式化代码（简单实现）
  void _formatCode() {
    String code = _controller.text;
    // 简单的代码格式化：调整缩进
    List<String> lines = code.split('\n');
    List<String> formattedLines = [];
    int indentLevel = 0;
    
    for (String line in lines) {
      String trimmedLine = line.trim();
      if (trimmedLine.isEmpty) {
        formattedLines.add('');
        continue;
      }
      
      // 减少缩进的关键词
      if (trimmedLine.startsWith('else') || 
          trimmedLine.startsWith('elif') || 
          trimmedLine.startsWith('except') || 
          trimmedLine.startsWith('finally') ||
          trimmedLine.startsWith('}')) {
        indentLevel = (indentLevel - 1).clamp(0, 10);
      }
      
      // 添加当前行
      formattedLines.add('    ' * indentLevel + trimmedLine);
      
      // 增加缩进的关键词
      if (trimmedLine.endsWith(':') || 
          trimmedLine.endsWith('{') ||
          trimmedLine.contains('if ') ||
          trimmedLine.contains('for ') ||
          trimmedLine.contains('while ') ||
          trimmedLine.contains('def ') ||
          trimmedLine.contains('class ')) {
        indentLevel++;
      }
    }
    
    String formattedCode = formattedLines.join('\n');
    setState(() {
      _controller.text = formattedCode;
    });
    widget.onAnswerChanged(formattedCode);
    HapticFeedback.mediumImpact();
  }

  /// 创建积木式代码块
  Widget _buildCodeBlock(String label, String code, Color color) {
    return GestureDetector(
      onTap: () => _insertCodeSnippet({"label": label, "code": code}),
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 2),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color, width: 2),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(Icons.view_module, color: color, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Icon(Icons.add, color: color, size: 16),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final correctAnswer = widget.question.answer ?? ''; // ignore: unused_local_variable
    final bool isCorrect =
        widget.interactionState == QuestionInteractionState.answerCorrect;

    // 样式颜色
    Color borderColor = Colors.white.withValues(alpha: 0.3);
    if (isShowingFeedback) {
      borderColor =
          isCorrect ? Colors.green : Colors.purple.shade300; // 编程题通常需要更复杂的评价
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 工具栏
        if (!isShowingFeedback) ...[
          Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                // 语言选择
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                  ),
                  child: DropdownButton<String>(
                    value: _currentLanguage,
                    isDense: true,
                    dropdownColor: Colors.black87,
                    underline: Container(),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                    items: [
                      DropdownMenuItem(value: 'python', child: Text('Python')),
                      DropdownMenuItem(value: 'javascript', child: Text('JavaScript')),
                      DropdownMenuItem(value: 'java', child: Text('Java')),
                    ],
                    onChanged: (value) {
                      if (value != null) _changeLanguage(value);
                    },
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 代码片段按钮
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _showSnippets = !_showSnippets;
                        if (_showSnippets) _showBlockMode = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: _showSnippets 
                            ? Colors.blue.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.code, color: Colors.white, size: 16),
                          SizedBox(width: 6),
                          Text('代码片段', style: TextStyle(color: Colors.white, fontSize: 14)),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 积木模式按钮
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _showBlockMode = !_showBlockMode;
                        if (_showBlockMode) _showSnippets = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      decoration: BoxDecoration(
                        color: _showBlockMode 
                            ? Colors.green.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.view_module, color: Colors.white, size: 16),
                          SizedBox(width: 6),
                          Text('积木编程', style: TextStyle(color: Colors.white, fontSize: 14)),
                        ],
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 格式化按钮
                GestureDetector(
                  onTap: _formatCode,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                    ),
                    child: const Icon(Icons.auto_fix_high, color: Colors.white, size: 20),
                  ),
                ),
                
                const SizedBox(width: 8),
                
                // 清空按钮
                if (_controller.text.isNotEmpty)
                  GestureDetector(
                    onTap: _clearCode,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                      ),
                      child: const Icon(Icons.clear_all, color: Colors.white, size: 20),
                    ),
                  ),
              ],
            ),
          ),
        ],
        
        // 代码编辑器
        ClipRRect(
          borderRadius: BorderRadius.circular(24.0),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(24.0),
                border: Border.all(color: borderColor, width: 2),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 编辑器顶部栏
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.code, color: Colors.green, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          '${_currentLanguage.toUpperCase()} 编程区域',
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Consolas',
                          ),
                        ),
                        const Spacer(),
                        if (!isShowingFeedback)
                          Text(
                            '${_controller.text.split('\n').length} 行',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                              fontFamily: 'Consolas',
                            ),
                          ),
                      ],
                    ),
                  ),

                  // 代码编辑区
                  TextField(
                    controller: _controller,
                    enabled: !isShowingFeedback,
                    maxLines: 12,
                    minLines: 8,
                    keyboardType: TextInputType.multiline,
                    textInputAction: TextInputAction.newline,
                    textCapitalization: TextCapitalization.none,
                    autocorrect: false,
                    enableSuggestions: false,
                    enableIMEPersonalizedLearning: false,
                    onChanged: (value) {
                      widget.onAnswerChanged(value);
                    },
                    onEditingComplete: () {
                      widget.onAnswerChanged(_controller.text);
                    },
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: 'Consolas',
                      height: 1.4,
                    ),
                    decoration: InputDecoration(
                      hintText: '// 在此编写${_currentLanguage}代码\n// 使用上方的代码片段或积木编程辅助编写',
                      hintStyle: TextStyle(
                        color: Colors.white.withValues(alpha: 0.4),
                        fontSize: 14,
                        fontFamily: 'Consolas',
                      ),
                      contentPadding: const EdgeInsets.all(16),
                      border: InputBorder.none,
                      fillColor: Colors.black.withValues(alpha: 0.6),
                      filled: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // 代码片段区域
        if (_showSnippets && !isShowingFeedback && _codeSnippets.isNotEmpty) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.code, color: Colors.blue, size: 16),
                    const SizedBox(width: 6),
                    Text(
                      '${_currentLanguage.toUpperCase()} 代码片段',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => setState(() => _showSnippets = false),
                      child: Icon(Icons.close, color: Colors.white.withValues(alpha: 0.7), size: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _codeSnippets.map((snippet) {
                    return GestureDetector(
                      onTap: () => _insertCodeSnippet(snippet),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.blue.withValues(alpha: 0.3),
                              Colors.cyan.withValues(alpha: 0.3),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              snippet['label']!,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              snippet['code']!.split('\n').first,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.8),
                                fontSize: 11,
                                fontFamily: 'Consolas',
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
        
        // 积木编程区域
        if (_showBlockMode && !isShowingFeedback) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.view_module, color: Colors.green, size: 16),
                    const SizedBox(width: 6),
                    const Text(
                      '积木编程',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => setState(() => _showBlockMode = false),
                      child: Icon(Icons.close, color: Colors.white.withValues(alpha: 0.7), size: 16),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                
                // 控制结构积木
                Text('控制结构', style: TextStyle(color: Colors.orange, fontSize: 12, fontWeight: FontWeight.bold)),
                const SizedBox(height: 6),
                _buildCodeBlock('循环', 'for i in range(n):\n    pass', Colors.orange),
                _buildCodeBlock('条件', 'if condition:\n    pass', Colors.orange),
                
                const SizedBox(height: 12),
                
                // 函数积木
                Text('函数', style: TextStyle(color: Colors.purple, fontSize: 12, fontWeight: FontWeight.bold)),
                const SizedBox(height: 6),
                _buildCodeBlock('定义函数', 'def function_name():\n    return result', Colors.purple),
                _buildCodeBlock('调用函数', 'function_name()', Colors.purple),
                
                const SizedBox(height: 12),
                
                // 输入输出积木
                Text('输入输出', style: TextStyle(color: Colors.cyan, fontSize: 12, fontWeight: FontWeight.bold)),
                const SizedBox(height: 6),
                _buildCodeBlock('输出', 'print("Hello")', Colors.cyan),
                _buildCodeBlock('输入', 'input("请输入:")', Colors.cyan),
              ],
            ),
          ),
        ],
        
        // 加载状态
        if (_isLoadingSnippets)
          Container(
            margin: const EdgeInsets.only(top: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
            ),
            child: const Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  '正在加载代码片段...',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
