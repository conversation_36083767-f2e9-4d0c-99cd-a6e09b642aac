import 'dart:math' as math;
import 'package:flutter/material.dart';

/// 仅绘制渐变描边的轻量组件（无毛玻璃、无背景）
/// - 用于与 LiquidGlass 叠加，恢复之前的“渐变边框”视觉
class AnimatedGradientBorder extends StatefulWidget {
  final double borderRadius;
  final double borderWidth;
  final List<Color> colors;
  final Duration duration;
  final bool animate;

  const AnimatedGradientBorder({
    super.key,
    this.borderRadius = 24.0,
    this.borderWidth = 2.5,
    this.colors = const [
      Color(0xFF8A00FF),
      Color(0xFFCB3EFF),
      Color(0xFFFF5C93),
    ],
    this.duration = const Duration(seconds: 6),
    this.animate = true,
  });

  @override
  State<AnimatedGradientBorder> createState() => _AnimatedGradientBorderState();
}

class _AnimatedGradientBorderState extends State<AnimatedGradientBorder>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;

  @override
  void initState() {
    super.initState();
    if (widget.animate) {
      _controller = AnimationController(vsync: this, duration: widget.duration)
        ..repeat();
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.animate || _controller == null) {
      // 不需要动画时绘制静态边框（t=0）且不触发重绘
      return CustomPaint(
        painter: _GradientStrokePainter(
          t: 0.0,
          borderRadius: widget.borderRadius,
          borderWidth: widget.borderWidth,
          colors: widget.colors,
        ),
      );
    }

    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _controller!,
        builder: (context, _) {
          return CustomPaint(
            painter: _GradientStrokePainter(
              t: _controller!.value,
              borderRadius: widget.borderRadius,
              borderWidth: widget.borderWidth,
              colors: widget.colors,
            ),
          );
        },
      ),
    );
  }
}

class _GradientStrokePainter extends CustomPainter {
  final double t;
  final double borderRadius;
  final double borderWidth;
  final List<Color> colors;

  _GradientStrokePainter({
    required this.t,
    required this.borderRadius,
    required this.borderWidth,
    required this.colors,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final inset = borderWidth / 2;
    final rect = Rect.fromLTWH(
      inset,
      inset,
      size.width - borderWidth,
      size.height - borderWidth,
    );

    final gradient = LinearGradient(
      colors: colors,
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      transform: GradientRotation(t * 2 * math.pi),
    );

    final paint =
        Paint()
          ..shader = gradient.createShader(rect)
          ..style = PaintingStyle.stroke
          ..strokeWidth = borderWidth;

    final rrect = RRect.fromRectAndRadius(rect, Radius.circular(borderRadius));
    canvas.drawRRect(rrect, paint);
  }

  @override
  bool shouldRepaint(covariant _GradientStrokePainter oldDelegate) =>
      t != oldDelegate.t ||
      borderRadius != oldDelegate.borderRadius ||
      borderWidth != oldDelegate.borderWidth ||
      colors != oldDelegate.colors;
}
