import 'package:flutter/material.dart';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class EnergyButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Function(int) onEnergyChange;

  // 静态实例，用于全局访问
  static EnergyButtonState? _instance;

  // 获取实例的方法，确保实例已初始化
  static EnergyButtonState? get instance => _instance;

  // 静态方法，用于全局访问
  static Future<bool> decreaseEnergy() async {
    if (_instance == null) return false;
    return _instance!.decreaseEnergy();
  }

  static int getCurrentEnergy() {
    if (_instance == null) return 15; // 默认值
    return _instance!._energyValue;
  }

  static Future<void> resetEnergyToFull() async {
    if (_instance == null) return;
    return _instance!.resetEnergyToFull();
  }

  // 设置实例的方法
  static void setInstance(EnergyButtonState? state) {
    _instance = state;
  }

  const EnergyButton({
    super.key,
    required this.onPressed,
    required this.onEnergyChange,
  });

  @override
  State<EnergyButton> createState() => EnergyButtonState();
}

class EnergyButtonState extends State<EnergyButton>
    with SingleTickerProviderStateMixin {
  int _energyValue = 15; // 初始默认值
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation; // ignore: unused_field
  Timer? _animationResetTimer;
  bool _isAnimating = false;
  Color _textColor = const Color(0xFFFF3B30); // 使用筛选按钮的红色

  static const String _energyStorageKey = 'user_energy_value';
  static const String _lastUpdateTimeKey = 'last_energy_update_time';

  @override
  void initState() {
    super.initState();

    // 注册为静态实例
    EnergyButton.setInstance(this);

    _loadEnergyValue();

    // 创建动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // 创建缩放动画
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween<double>(begin: 1.0, end: 1.2), weight: 30),
      TweenSequenceItem(tween: Tween<double>(begin: 1.2, end: 0.9), weight: 30),
      TweenSequenceItem(tween: Tween<double>(begin: 0.9, end: 1.0), weight: 40),
    ]).animate(_animationController);

    _animationResetTimer = null;

    // 设置每日0点自动刷新
    _scheduleEnergyReset();
  }

  @override
  void dispose() {
    // 如果当前实例是静态实例，则清除
    if (EnergyButton.instance == this) {
      EnergyButton.setInstance(null);
    }

    _animationController.dispose();
    _animationResetTimer?.cancel();
    super.dispose();
  }

  // 加载能量值
  Future<void> _loadEnergyValue() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 先获取最后更新时间
      final lastUpdateTimeMillis = prefs.getInt(_lastUpdateTimeKey);
      if (lastUpdateTimeMillis != null) {
        final lastUpdateTime = DateTime.fromMillisecondsSinceEpoch(
          lastUpdateTimeMillis,
        );
        final now = DateTime.now();

        // 检查是否过了午夜（新的一天）
        if (lastUpdateTime.day != now.day ||
            lastUpdateTime.month != now.month ||
            lastUpdateTime.year != now.year) {
          // 新的一天，重置能量
          await _saveEnergyValue(15);
          if (mounted) {
            setState(() {
              _energyValue = 15;
            });
          }
          return;
        }
      }

      // 读取存储的能量值
      final energyValue = prefs.getInt(_energyStorageKey);
      if (energyValue != null) {
        if (mounted) {
          setState(() {
            _energyValue = energyValue;
          });
          widget.onEnergyChange(_energyValue);
        }
      } else {
        // 首次运行，保存默认值
        await _saveEnergyValue(15);
      }
    } catch (e) {
      debugPrint('加载能量值失败: $e');
    }
  }

  // 保存能量值
  Future<void> _saveEnergyValue(int value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_energyStorageKey, value);
      await prefs.setInt(
        _lastUpdateTimeKey,
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      debugPrint('保存能量值失败: $e');
    }
  }

  // 设置每天0点自动刷新
  void _scheduleEnergyReset() {
    // 计算距离下一个0点的时间
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final timeUntilMidnight = tomorrow.difference(now);

    // 设置定时器
    Timer(timeUntilMidnight, () async {
      // 重置能量值
      await _saveEnergyValue(15);
      if (mounted) {
        setState(() {
          _energyValue = 15;
        });
        widget.onEnergyChange(_energyValue);
      }

      // 动画提示能量已刷新
      _animateValueChange(false);

      // 继续设置下一次刷新
      _scheduleEnergyReset();
    });
  }

  // 减少能量
  Future<bool> decreaseEnergy() async {
    if (_energyValue <= 0) {
      return false; // 能量不足
    }

    if (mounted) {
      setState(() {
        _energyValue--;
      });
    }

    await _saveEnergyValue(_energyValue);
    widget.onEnergyChange(_energyValue);

    // 触发减少动画
    _animateValueChange(true);

    return true;
  }

  // 获取当前能量值
  int getCurrentEnergy() {
    return _energyValue;
  }

  // 重置能量值（如通过看广告）
  Future<void> resetEnergyToFull() async {
    if (mounted) {
      setState(() {
        _energyValue = 15;
      });
    }

    await _saveEnergyValue(15);
    widget.onEnergyChange(_energyValue);

    // 触发增加动画
    _animateValueChange(false);
  }

  // 触发能量值变化的动画
  void _animateValueChange(bool isDecrease) {
    if (_isAnimating || !mounted) return;

    setState(() {
      _isAnimating = true;
      _textColor = isDecrease ? Colors.white.withValues(alpha: 0.7) : Colors.white;
    });

    _animationController.forward(from: 0.0).then((_) {
      // 动画结束后重置状态
      if (_animationResetTimer != null) {
        _animationResetTimer!.cancel();
      }

      _animationResetTimer = Timer(const Duration(milliseconds: 300), () {
        if (mounted) {
          setState(() {
            _isAnimating = false;
            _textColor = Colors.white; // 使用白色替代红色
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // 修改边框透明度为更低值，让整体更轻盈
    // 边框渐变颜色，调整透明度使整体更轻盈
    final Color borderColorTop = Colors.white.withValues(alpha: 0.18);
    final Color borderColorBottom = Colors.white.withValues(alpha: 0.22);
    // 内部填充色为纯白色
    final Color fillColor = Colors.white;
    // 电量指示也使用白色
    final Color energyColor = Colors.white.withValues(alpha: 0.9);

    return GestureDetector(
      onTap: widget.onPressed,
      child: Container(
        height: 36, // 增加整体高度
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 电池主体阴影 - 更轻微的阴影
            Container(
              width: 40, // 增加宽度
              height: 22, // 增加高度
              margin: const EdgeInsets.only(top: 1.2, right: 1.2), // 减小阴影偏移
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.0), // 调整圆角
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08), // 降低阴影不透明度
                    blurRadius: 1.5, // 减小阴影模糊半径
                    offset: const Offset(0, 0.6), // 减小阴影偏移
                  ),
                ],
              ),
            ),

            // 电池主体
            Container(
              width: 40, // 增加宽度
              height: 22, // 增加高度
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [borderColorTop, borderColorBottom],
                  stops: const [0.4, 1.0],
                ),
                borderRadius: BorderRadius.circular(6.0), // 调整圆角
              ),
              child: Padding(
                padding: const EdgeInsets.all(3.8), // 调整内边距
                child: Container(
                  decoration: BoxDecoration(
                    color: fillColor,
                    borderRadius: BorderRadius.circular(3.5), // 调整内部圆角
                  ),
                  child: Container(
                    margin: EdgeInsets.only(right: _calculateBatteryMargin()),
                    decoration: BoxDecoration(
                      color: energyColor,
                      borderRadius: BorderRadius.circular(3.0), // 调整内部圆角
                    ),
                  ),
                ),
              ),
            ),

            // 电池正极
            Positioned(
              right: -1,
              child: Container(
                width: 4.0, // 增加电池正极宽度
                height: 11.0, // 增加电池正极高度
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [borderColorTop, borderColorBottom],
                    stops: const [0.4, 1.0],
                  ),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(2.0),
                    bottomRight: Radius.circular(2.0),
                  ),
                ),
              ),
            ),

            // 能量数值，居中显示
            Positioned(
              // 水平居中，不使用固定的right值
              child: AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      // 增强边框效果，改为黑色描边
                      Text(
                        '$_energyValue',
                        style: TextStyle(
                          fontSize: 12, // 增加字体大小
                          fontWeight: FontWeight.w800, // 增加字体粗细
                          foreground:
                              Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth =
                                    3.0 // 增加描边宽度
                                ..color = Colors.black.withValues(alpha: 0.7), // 黑色描边
                          fontFamily:
                              Platform.isIOS ? '.SF Pro Text' : 'Roboto',
                        ),
                      ),
                      // 能量数值文字内部
                      Text(
                        '$_energyValue',
                        style: TextStyle(
                          color:
                              _isAnimating
                                  ? _textColor
                                  : Colors.white, // 使用白色增加可见度
                          fontSize: 12, // 增加字体大小
                          fontWeight: FontWeight.w800, // 增加字体粗细
                          fontFamily:
                              Platform.isIOS ? '.SF Pro Text' : 'Roboto',
                          // 添加轻微阴影增强对比度
                          shadows: [
                            Shadow(
                              blurRadius: 1.0,
                              color: Colors.black.withValues(alpha: 0.3),
                              offset: const Offset(0, 0.5),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 根据电量计算内部填充空间
  double _calculateBatteryMargin() {
    // 最大电量为15，计算填充比例
    final fillPercentage = 1 - (_energyValue / 15);
    // 内部最大宽度调整为适应更大的电池
    return fillPercentage * 32; // 增加填充宽度
  }

  // 获取电池颜色 - 已不再使用于UI，仅在动画时可能使用
  Color _getBatteryColor() { // ignore: unused_element
    if (_energyValue <= 3) {
      return Colors.red;
    } else if (_energyValue <= 7) {
      return Colors.orange;
    } else {
      return Colors.white; // 改为白色
    }
  }
}
