import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../core/routing/app_router.dart';

/// 认证包装器，用于包装需要用户登录的页面
/// 当用户未登录时自动重定向到登录页面
class AuthWrapper extends StatefulWidget {
  final Widget child;
  final bool requireAuth;

  const AuthWrapper({super.key, required this.child, this.requireAuth = true});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  // 存储 UserProvider 的引用，避免在 dispose 中获取
  UserProvider? _userProvider;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (widget.requireAuth) {
      // 在这里安全地获取 Provider 引用
      _userProvider = Provider.of<UserProvider>(context, listen: false);
    }
  }

  @override
  void initState() {
    super.initState();
    if (widget.requireAuth) {
      // 添加登录状态监听器，但在 post frame 回调中进行
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_userProvider != null) {
          _checkLoginStatus(_userProvider!);
          _userProvider!.addListener(_onUserProviderChanged);
        }
      });
    }
  }

  @override
  void dispose() {
    if (widget.requireAuth && _userProvider != null) {
      // 使用之前保存的引用，而不是重新获取
      _userProvider!.removeListener(_onUserProviderChanged);
    }
    super.dispose();
  }

  void _onUserProviderChanged() {
    if (_userProvider != null) {
      _checkLoginStatus(_userProvider!);
    }
  }

  void _checkLoginStatus(UserProvider userProvider) {
    if (!userProvider.isLoggedIn && !userProvider.isLoading) {
      // 使用统一路由进行导航
      AppRouter.toLogin(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.requireAuth) {
      return widget.child;
    }

    final userProvider = Provider.of<UserProvider>(context);

    if (userProvider.isLoading) {
      // 显示加载指示器
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (!userProvider.isLoggedIn) {
      // 立即显示登录页面，而不是等待监听器回调
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppRouter.toLogin(context);
      });
      // 返回一个占位视图
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    // 用户已登录，显示子组件
    return widget.child;
  }
}
