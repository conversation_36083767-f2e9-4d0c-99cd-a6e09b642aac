import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../core/routing/app_router.dart';
import 'package:provider/provider.dart';
import 'package:wakeup_app/l10n/app_localizations.dart';
import '../providers/user_provider.dart';
import '../providers/locale_provider.dart';
import '../widgets/animated_avatar_widget.dart';
import '../services/auth_service.dart';
// import '../pages/main/profile_edit_page.dart';

// 显示账户底部弹出页面的方法
void showAccountBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => const AccountBottomSheet(),
  );
}

class AccountBottomSheet extends StatefulWidget {
  const AccountBottomSheet({super.key});

  @override
  State<AccountBottomSheet> createState() => _AccountBottomSheetState();
}

class _AccountBottomSheetState extends State<AccountBottomSheet> {
  bool _notificationsEnabled = true;
  bool _findFriendsEnabled = false;

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final isLoggedIn = userProvider.isLoggedIn;
    final appLocalizations = AppLocalizations.of(context)!;

    // 获取屏幕高度的90%作为底部弹出页面的高度
    final height = MediaQuery.of(context).size.height * 0.90;

    return Container(
      height: height,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
          child: Container(
            decoration: BoxDecoration(
              color: CupertinoColors.black.withValues(alpha: 0.7),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              children: [
                // 拖动指示器 和 顶部栏 (标题 + 完成按钮)
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Column(
                    children: [
                      // 拖动指示器 - 此部分将被移除
                      // Container(
                      //   width: 40,
                      //   height: 5,
                      //   margin: const EdgeInsets.only(top: 8, bottom: 8),
                      //   decoration: BoxDecoration(
                      //     color: Colors.grey.withValues(alpha: 0.5),
                      //     borderRadius: BorderRadius.circular(2.5),
                      //   ),
                      // ),
                      // 标题和完成按钮
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 8.0,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // 占位，保持标题居中
                            SizedBox(width: 60),
                            Expanded(
                              child: Text(
                                "账户", // TODO: Consider localization if needed appLocalizations.account,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: CupertinoColors.white,
                                  fontSize: 17,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            CupertinoButton(
                              padding: EdgeInsets.zero,
                              child: Text(
                                "完成", // TODO: Consider localization appLocalizations.done,
                                style: TextStyle(
                                  color:
                                      CupertinoColors
                                          .white, // Changed from activeBlue to white
                                  fontSize: 17,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // 主内容
                Padding(
                  padding: const EdgeInsets.only(
                    top: 50,
                  ), // Adjusted top padding after removing indicator
                  child: ListView(
                    padding: const EdgeInsets.fromLTRB(
                      16,
                      16,
                      16,
                      50,
                    ), // Kept padding as is, header is smaller
                    children: [
                      // 用户信息
                      _buildUserInfoSection(isLoggedIn, context),

                      const SizedBox(height: 30),

                      // 若未登录，显示登录/注册按钮
                      if (!isLoggedIn)
                        _buildActionButton(
                          label: appLocalizations.settings,
                          icon: CupertinoIcons.person_fill,
                          onPressed: () {
                            Navigator.of(context).pop();
                            // TODO: 跳转到登录页面
                          },
                        ),

                      // 通知设置
                      _buildSectionHeader(
                        appLocalizations.notificationSettings,
                      ),
                      _buildSwitchTile(
                        title: appLocalizations.pushNotifications,
                        subtitle: appLocalizations.receiveNotifications,
                        value: _notificationsEnabled,
                        onChanged: (value) {
                          setState(() {
                            _notificationsEnabled = value;
                          });
                        },
                      ),

                      const SizedBox(height: 20),

                      // 语言设置
                      _buildSectionHeader(appLocalizations.languageSettings),
                      _buildLanguageSelector(),

                      const SizedBox(height: 20),

                      // 隐私设置
                      _buildSectionHeader(appLocalizations.privacySettings),
                      _buildSwitchTile(
                        title: appLocalizations.findFriends,
                        subtitle: appLocalizations.findFriendsDescription,
                        value: _findFriendsEnabled,
                        onChanged: (value) {
                          setState(() {
                            _findFriendsEnabled = value;
                          });
                        },
                      ),

                      const SizedBox(height: 20),

                      // 账户设置
                      if (isLoggedIn) ...[
                        _buildActionButton(
                          label: appLocalizations.settings,
                          icon: CupertinoIcons.settings,
                          onPressed: () {
                            // TODO: 跳转到账户设置页面
                          },
                        ),

                        const SizedBox(height: 20),

                        // 退出登录按钮
                        _buildActionButton(
                          label: appLocalizations.logout,
                          icon: CupertinoIcons.square_arrow_right,
                          onPressed: () {
                            userProvider.clearUser();
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 用户信息区域
  Widget _buildUserInfoSection(bool isLoggedIn, BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    final userProvider = Provider.of<UserProvider>(context);
    final avatarUrl = userProvider.avatarUrl;
    final bool hasAvatar =
        isLoggedIn && avatarUrl != null && avatarUrl.isNotEmpty;

    return GestureDetector(
      onTap:
          isLoggedIn
              ? () {
                Navigator.of(context).pop(); // 先关闭底部弹窗
                AppRouter.toProfileEdit(context);
              }
              : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        margin: const EdgeInsets.only(
          bottom: 20,
          top: 5,
        ), // Added top margin for spacing from header
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.12),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // 头像
            ClipOval(
              child: SizedBox(
                width: 50,
                height: 50,
                child:
                    hasAvatar
                        ? Image.network(
                          avatarUrl,
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return AnimatedAvatarWidget(size: 50);
                          },
                        )
                        : AnimatedAvatarWidget(size: 50),
              ),
            ),
            const SizedBox(width: 16),

            // 用户名/登录提示 和 查看资料
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FutureBuilder<Map<String, dynamic>?>(
                    future:
                        isLoggedIn
                            ? AuthService.getUserInfo(
                              userProvider.userId,
                              userProvider.token ?? '',
                            )
                            : Future.value(null),
                    builder: (context, snapshot) {
                      String displayName = appLocalizations.notLoggedIn;

                      if (isLoggedIn) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          displayName = "加载中...";
                        } else if (snapshot.hasData && snapshot.data != null) {
                          // 优先显示昵称，如果没有昵称则显示用户名
                          displayName =
                              snapshot.data!['nickname'] ??
                              (snapshot.data!['name'] ??
                                  '用户${userProvider.userId}');
                        } else {
                          displayName = '用户${userProvider.userId}';
                        }
                      }

                      return Text(
                        displayName,
                        style: const TextStyle(
                          color: CupertinoColors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                  if (isLoggedIn)
                    Padding(
                      padding: const EdgeInsets.only(top: 2.0),
                      child: Text(
                        appLocalizations.editProfile, // Assumed to be "编辑个人资料"
                        style: TextStyle(
                          color: CupertinoColors.systemGrey.withValues(
                            alpha: 0.8,
                          ),
                          fontSize: 14,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // 右侧箭头 (仅在登录后显示)
            if (isLoggedIn)
              Icon(
                CupertinoIcons.chevron_forward,
                color: CupertinoColors.systemGrey.withValues(alpha: 0.8),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  // 小节标题
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Text(
        title,
        style: const TextStyle(
          color: CupertinoColors.white,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // 开关项
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white.withValues(
          alpha: 0.15,
        ), // Slightly increased opacity
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        title: Text(
          title,
          style: const TextStyle(
            color: CupertinoColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: CupertinoColors.white.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
        trailing: CupertinoSwitch(
          value: value,
          onChanged: onChanged,
          activeTrackColor:
              CupertinoColors.systemGrey2, // Changed from systemRed
        ),
      ),
    );
  }

  // 语言选择器
  Widget _buildLanguageSelector() {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isEnglish = localeProvider.locale.languageCode == 'en';
    final appLocalizations = AppLocalizations.of(context)!;

    return InkWell(
      borderRadius: BorderRadius.circular(12),
      onTap: () {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                backgroundColor: Colors.grey.shade900,
                title: Text(
                  appLocalizations.language,
                  style: const TextStyle(color: Colors.white),
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      title: const Text(
                        'English',
                        style: TextStyle(color: Colors.white),
                      ),
                      trailing:
                          isEnglish
                              ? const Icon(
                                Icons.check,
                                color: Colors.white,
                              ) // Changed from Colors.blue
                              : null,
                      onTap: () {
                        localeProvider.setLocale(const Locale('en'));
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      title: const Text(
                        '中文',
                        style: TextStyle(color: Colors.white),
                      ),
                      trailing:
                          !isEnglish
                              ? const Icon(
                                Icons.check,
                                color: Colors.white,
                              ) // Changed from Colors.blue
                              : null,
                      onTap: () {
                        localeProvider.setLocale(const Locale('zh'));
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(
            alpha: 0.15,
          ), // Slightly increased opacity
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(CupertinoIcons.globe, color: CupertinoColors.white),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    appLocalizations.language,
                    style: const TextStyle(
                      color: CupertinoColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isEnglish ? 'English' : '中文',
                    style: TextStyle(
                      color: CupertinoColors.systemGrey,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              CupertinoIcons.forward,
              color: CupertinoColors.systemGrey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // 操作按钮
  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: onPressed,
        child: ListTile(
          leading: Icon(icon, color: CupertinoColors.white),
          title: Text(
            label,
            style: const TextStyle(
              color: CupertinoColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          trailing: const Icon(
            CupertinoIcons.chevron_right,
            color: CupertinoColors.white,
          ),
        ),
      ),
    );
  }
}
