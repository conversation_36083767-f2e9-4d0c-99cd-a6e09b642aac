import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// 选择题选项组件
/// 用于单选题和多选题，根据isCheckbox参数区分显示风格
class ChoiceOptionWidget extends StatelessWidget {
  final String optionText;
  final bool isSelected;
  final bool showCorrect;
  final bool showIncorrect;
  final bool isCheckbox;
  final VoidCallback? onTap;
  final IconData? trailingIcon;

  const ChoiceOptionWidget({
    super.key,
    required this.optionText,
    this.isSelected = false,
    this.showCorrect = false,
    this.showIncorrect = false,
    this.isCheckbox = false,
    this.onTap,
    this.trailingIcon,
  });

  @override
  Widget build(BuildContext context) {
    // 根据状态设置颜色
    Color backgroundColor;
    Color borderColor;
    Color textColor;
    IconData? displayIcon;

    // 确定选项的外观
    if (showCorrect) {
      // 正确答案的颜色
      backgroundColor = Colors.green.withValues(alpha: 0.1);
      borderColor = Colors.green;
      textColor = Colors.white;
      displayIcon = CupertinoIcons.check_mark_circled_solid;
    } else if (showIncorrect) {
      // 错误选择的颜色
      backgroundColor = Colors.red.withValues(alpha: 0.1);
      borderColor = Colors.red;
      textColor = Colors.white;
      displayIcon = CupertinoIcons.xmark_circle_fill;
    } else if (isSelected) {
      // 选中状态的颜色
      backgroundColor = const Color(0xFF4c2956).withValues(alpha: 0.7);
      borderColor = Colors.purple.shade300;
      textColor = Colors.white;
      displayIcon =
          isCheckbox
              ? CupertinoIcons.checkmark_square_fill
              : CupertinoIcons.circle_fill;
    } else {
      // 未选中状态的颜色
      backgroundColor = Colors.black.withValues(alpha: 0.5);
      borderColor = Colors.white.withValues(alpha: 0.3);
      textColor = Colors.white;
      displayIcon = isCheckbox ? CupertinoIcons.square : CupertinoIcons.circle;
    }

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.symmetric(vertical: 8.0),
        child: RepaintBoundary(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24.0),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 16.0,
              ),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(24.0),
                border: Border.all(color: borderColor, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    spreadRadius: 1,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      optionText,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                        fontFamily: 'Noto Sans SC',
                      ),
                    ),
                  ),
                  Icon(
                    (trailingIcon ?? displayIcon),
                    color:
                        showCorrect
                            ? Colors.green
                            : showIncorrect
                            ? Colors.red
                            : isSelected
                            ? Colors.purple.shade200
                            : Colors.white.withValues(alpha: 0.7),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
