import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import '../constants/fonts.dart';

/// 毛玻璃暗黑透明底部导航栏
class GlassBottomNavigationBar extends StatelessWidget {
  final List<BottomNavigationBarItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final double blur;
  final double opacity;
  final bool darkMode;
  final double height;

  const GlassBottomNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.blur = 10.0,
    this.opacity = 0.3,
    this.darkMode = true,
    this.height = 52.0,
  });

  @override
  Widget build(BuildContext context) {
    // 选中和未选中项的颜色
    const selectedColor = Colors.white;
    final unselectedColor = Colors.grey[400];

    return ClipRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          height: height + MediaQuery.of(context).padding.bottom,
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom,
          ),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: opacity),
            border: Border(
              top: BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 0.5),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(items.length, (index) {
              final isSelected = index == currentIndex;
              return _buildNavItem(
                context: context,
                item: items[index],
                isSelected: isSelected,
                selectedColor: selectedColor,
                unselectedColor: unselectedColor!,
                onTap: () => onTap(index),
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required BottomNavigationBarItem item,
    required bool isSelected,
    required Color selectedColor,
    required Color unselectedColor,
    required VoidCallback onTap,
  }) {
    // 创建导航文本
    Widget buildNavText(String text, bool isSelected) {
      final textColor = isSelected ? selectedColor : unselectedColor;

      // 为iOS设备使用更粗的字体效果，但保持样式一致
      if (Platform.isIOS || Platform.isMacOS) {
        return Align(
          alignment: Alignment.center,
          child: Text(
            text,
            style: AppFonts.createLatinStyle(
              fontSize: 12.0,
              fontWeight: FontWeight.w600,
              color: textColor,
              isDisplayFont: true,
            ),
            textAlign: TextAlign.center,
          ),
        );
      }

      // 非iOS设备使用常规文本
      return Text(
        text,
        style: AppFonts.createLatinStyle(
          fontSize: 12.0,
          fontWeight: FontWeight.w500,
          color: textColor,
          isDisplayFont: true,
        ),
        textAlign: TextAlign.center,
      );
    }

    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 2.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标
            Icon(
              (item.activeIcon as Icon?)?.icon ?? (item.icon as Icon).icon,
              color: isSelected ? selectedColor : unselectedColor,
              size: 26.0,
            ),

            const SizedBox(height: 2.0),
            // 标签文字
            item.label != null
                ? buildNavText(item.label!, isSelected)
                : const SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
