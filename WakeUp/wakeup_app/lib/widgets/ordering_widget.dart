import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/quiz_model.dart';
import '../constants/quiz_constants.dart';
import '../services/auth_service.dart';
import '../core/utils/logger.dart';

/// 排序题组件
class OrderingWidget extends StatefulWidget {
  final Question question;
  final List<OrderingItem>? userOrder;
  final Function(List<OrderingItem>) onAnswerChanged;
  final QuestionInteractionState interactionState;

  const OrderingWidget({
    super.key,
    required this.question,
    this.userOrder,
    required this.onAnswerChanged,
    required this.interactionState,
  });

  @override
  State<OrderingWidget> createState() => _OrderingWidgetState();
}

class _OrderingWidgetState extends State<OrderingWidget> {
  late List<OrderingItem> _userOrderItems;
  List<Map<String, dynamic>> _smartSuggestions = [];
  bool _isLoadingSuggestions = false;
  bool _showSuggestions = false;
  List<int> _suggestedOrder = [];

  @override
  void initState() {
    super.initState();
    _initializeOrderingData();
    _loadSmartSuggestions();
  }

  @override
  void didUpdateWidget(OrderingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userOrder != widget.userOrder) {
      _initializeOrderingData();
    }
  }

  void _initializeOrderingData() {
    // 如果已经有用户排序，则使用；否则使用打乱后的题目排序项
    if (widget.userOrder != null && widget.userOrder!.isNotEmpty) {
      _userOrderItems = List<OrderingItem>.from(widget.userOrder!);
    } else {
      // 获取题目的排序项，并随机打乱顺序
      _userOrderItems = List<OrderingItem>.from(
        widget.question.orderingItems ?? [],
      )..shuffle();
    }
  }

  /// 加载智能排序建议
  Future<void> _loadSmartSuggestions() async {
    if (widget.interactionState != QuestionInteractionState.waitingForSelection) {
      return;
    }

    setState(() {
      _isLoadingSuggestions = true;
    });

    try {
      final response = await http.get(
        Uri.parse('${AuthService.baseUrl}/api/smart_hints/ordering/${widget.question.id}'),
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          setState(() {
            _smartSuggestions = List<Map<String, dynamic>>.from(
              data['suggestions'] ?? []
            );
            _showSuggestions = _smartSuggestions.isNotEmpty;
          });
        }
      } else {
        _generateSmartSuggestions();
      }
    } catch (e) {
      AppLogger.error('加载排序题智能建议失败', error: e);
      _generateSmartSuggestions();
    } finally {
      setState(() {
        _isLoadingSuggestions = false;
      });
    }
  }

  /// 生成智能排序建议
  void _generateSmartSuggestions() {
    List<Map<String, dynamic>> suggestions = [];
    
    // 分析序号和时间关键词
    for (int i = 0; i < _userOrderItems.length; i++) {
      final item = _userOrderItems[i];
      String reason = '';
      int confidence = 50;
      
      // 检查是否包含序号关键词
      final content = item.content.toLowerCase();
      if (content.contains('首先') || content.contains('第一') || content.contains('开始')) {
        reason = '包含起始关键词';
        confidence = 90;
        suggestions.add({
          'item_id': item.id,
          'suggested_position': 1,
          'reason': reason,
          'confidence': confidence,
          'content': item.content
        });
      } else if (content.contains('最后') || content.contains('终于') || content.contains('结束')) {
        reason = '包含结束关键词';
        confidence = 90;
        suggestions.add({
          'item_id': item.id,
          'suggested_position': _userOrderItems.length,
          'reason': reason,
          'confidence': confidence,
          'content': item.content
        });
      } else if (content.contains('其次') || content.contains('然后') || content.contains('接下来')) {
        reason = '包含连接关键词';
        confidence = 75;
        suggestions.add({
          'item_id': item.id,
          'suggested_position': 2,
          'reason': reason,
          'confidence': confidence,
          'content': item.content
        });
      }
    }
    
    // 按置信度排序
    suggestions.sort((a, b) => b['confidence'].compareTo(a['confidence']));
    
    setState(() {
      _smartSuggestions = suggestions;
      _showSuggestions = suggestions.isNotEmpty;
    });
  }

  /// 应用智能排序建议
  void _applySuggestion(Map<String, dynamic> suggestion) {
    final itemId = suggestion['item_id'] as int;
    final suggestedPosition = suggestion['suggested_position'] as int;
    
    // 找到对应的项目
    final itemIndex = _userOrderItems.indexWhere((item) => item.id == itemId);
    if (itemIndex != -1 && suggestedPosition >= 1 && suggestedPosition <= _userOrderItems.length) {
      setState(() {
        final item = _userOrderItems.removeAt(itemIndex);
        _userOrderItems.insert(suggestedPosition - 1, item);
        widget.onAnswerChanged(_userOrderItems);
      });
      
      HapticFeedback.mediumImpact();
    }
  }

  /// 智能排序（全自动）
  void _smartSort() {
    // 基于关键词和内容分析进行自动排序
    List<OrderingItem> smartSorted = List.from(_userOrderItems);
    
    // 先把明显的首尾项放在正确位置
    OrderingItem? firstItem;
    OrderingItem? lastItem;
    List<OrderingItem> middleItems = [];
    
    for (var item in smartSorted) {
      final content = item.content.toLowerCase();
      if (content.contains('首先') || content.contains('第一') || content.contains('开始')) {
        firstItem = item;
      } else if (content.contains('最后') || content.contains('终于') || content.contains('结束')) {
        lastItem = item;
      } else {
        middleItems.add(item);
      }
    }
    
    // 重新排列
    List<OrderingItem> newOrder = [];
    if (firstItem != null) newOrder.add(firstItem);
    newOrder.addAll(middleItems);
    if (lastItem != null) newOrder.add(lastItem);
    
    setState(() {
      _userOrderItems = newOrder;
      widget.onAnswerChanged(_userOrderItems);
    });
    
    HapticFeedback.heavyImpact();
  }

  /// 随机打乱顺序
  void _shuffleOrder() {
    setState(() {
      _userOrderItems.shuffle();
      widget.onAnswerChanged(_userOrderItems);
    });
    
    HapticFeedback.lightImpact();
  }

  /// 反向排序
  void _reverseOrder() {
    setState(() {
      _userOrderItems = _userOrderItems.reversed.toList();
      widget.onAnswerChanged(_userOrderItems);
    });
    
    HapticFeedback.mediumImpact();
  }

  @override
  Widget build(BuildContext context) {
    final isShowingFeedback =
        widget.interactionState != QuestionInteractionState.waitingForSelection;
    final orderedItems = List<OrderingItem>.from(_userOrderItems); // ignore: unused_local_variable

    // 获取正确排序的项目（用于显示答案）
    final correctItems = List<OrderingItem>.from( // ignore: unused_local_variable
      widget.question.orderingItems ?? [],
    )..sort((a, b) => a.correctOrder.compareTo(b.correctOrder));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 提示信息和操作按钮
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "请拖拽下列选项，按正确顺序排列：",
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'Noto Sans SC',
              ),
            ),
            if (!isShowingFeedback) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  // 智能排序按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: _smartSort,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.green.withValues(alpha: 0.5)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.auto_fix_high, color: Colors.green, size: 16),
                            const SizedBox(width: 6),
                            Text(
                              '智能排序',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 智能建议按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _showSuggestions = !_showSuggestions;
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        decoration: BoxDecoration(
                          color: _showSuggestions 
                              ? Colors.blue.withValues(alpha: 0.3)
                              : Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.lightbulb_outline, color: Colors.amber, size: 16),
                            const SizedBox(width: 6),
                            Text(
                              '智能建议',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 反向排序按钮
                  GestureDetector(
                    onTap: _reverseOrder,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                      ),
                      child: Icon(Icons.flip_to_back, color: Colors.white, size: 16),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 随机打乱按钮
                  GestureDetector(
                    onTap: _shuffleOrder,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                      ),
                      child: Icon(Icons.shuffle, color: Colors.white, size: 16),
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),
          ],
        ),

        // 排序区域
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(24),
          ),
          child: ReorderableListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _userOrderItems.length,
            onReorder: (int oldIndex, int newIndex) {
              // 只在等待选择状态下允许排序
              if (widget.interactionState ==
                  QuestionInteractionState.waitingForSelection) {
                setState(() {
                  if (oldIndex < newIndex) {
                    newIndex -= 1;
                  }
                  final item = _userOrderItems.removeAt(oldIndex);
                  _userOrderItems.insert(newIndex, item);

                  // 通知父组件
                  widget.onAnswerChanged(_userOrderItems);
                });
              }
            },
            itemBuilder: (context, index) {
              final item = _userOrderItems[index];

              // 判断当前位置是否正确
              bool isCorrect = false;
              if (isShowingFeedback) {
                isCorrect = item.correctOrder == index + 1;
              }

              // 边框颜色
              Color borderColor = Colors.white.withValues(alpha: 0.2);
              if (isShowingFeedback) {
                borderColor = isCorrect ? Colors.green : Colors.red;
              }

              return Container(
                key: ValueKey(item.id),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(color: borderColor, width: 2),
                ),
                child: ListTile(
                  leading: Container(
                    width: 32,
                    height: 32,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.purple.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.purple.shade300,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        fontFamily: 'Noto Sans SC',
                      ),
                    ),
                  ),
                  title: Text(
                    item.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Noto Sans SC',
                    ),
                  ),
                  trailing:
                      isShowingFeedback
                          ? Icon(
                            isCorrect
                                ? CupertinoIcons.check_mark_circled_solid
                                : CupertinoIcons.xmark_circle_fill,
                            color: isCorrect ? Colors.green : Colors.red,
                            size: 24,
                          )
                          : const Icon(CupertinoIcons.bars, color: Colors.grey),
                ),
              );
            },
          ),
        ),
        
        // 智能建议区域
        if (_showSuggestions && !isShowingFeedback && _smartSuggestions.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.lightbulb_outline, color: Colors.amber, size: 16),
                    const SizedBox(width: 6),
                    Text(
                      '排序建议',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => setState(() => _showSuggestions = false),
                      child: Icon(
                        Icons.close,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Column(
                  children: _smartSuggestions.take(3).map((suggestion) {
                    final confidence = suggestion['confidence'] as int;
                    final position = suggestion['suggested_position'] as int;
                    
                    return GestureDetector(
                      onTap: () => _applySuggestion(suggestion),
                      child: Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.amber.withValues(alpha: 0.2),
                              Colors.orange.withValues(alpha: 0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.amber.withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '第${position}位',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    suggestion['content'] ?? '',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: confidence >= 80
                                        ? Colors.green.withValues(alpha: 0.3)
                                        : confidence >= 60
                                            ? Colors.yellow.withValues(alpha: 0.3)
                                            : Colors.orange.withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Text(
                                    '${confidence}%',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 11,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              suggestion['reason'] ?? '智能分析建议',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
        
        // 加载状态
        if (_isLoadingSuggestions)
          Container(
            margin: const EdgeInsets.only(top: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '正在生成排序建议...',
                  style: TextStyle(color: Colors.white, fontSize: 14),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
