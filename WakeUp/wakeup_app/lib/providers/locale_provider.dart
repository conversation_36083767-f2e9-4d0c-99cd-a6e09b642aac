import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  Locale _locale = const Locale('en');

  // 获取当前语言
  Locale get locale => _locale;

  // 初始化，从持久化存储中读取语言设置
  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final storedLocale = prefs.getString('locale');
    if (storedLocale != null) {
      _locale = Locale(storedLocale);
    }
    notifyListeners();
  }

  // 设置新语言
  Future<void> setLocale(Locale newLocale) async {
    if (newLocale.languageCode != _locale.languageCode) {
      _locale = newLocale;

      // 保存到持久化存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('locale', newLocale.languageCode);

      notifyListeners();
    }
  }

  // 切换语言（英文/中文）
  Future<void> toggleLocale() async {
    final newLanguageCode = _locale.languageCode == 'en' ? 'zh' : 'en';
    await setLocale(Locale(newLanguageCode));
  }
}
