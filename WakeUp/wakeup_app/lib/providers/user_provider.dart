import 'package:flutter/material.dart';
import 'package:wakeup_app/core/utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class UserProvider extends ChangeNotifier {
  int _userId = 0;
  String? _token;
  String? _avatarUrl;
  String? _nickname;
  bool _isLoading = true;
  bool _courseChanged = false;

  int get userId => _userId;
  String? get token => _token;
  String? get avatarUrl => _avatarUrl;
  String? get nickname => _nickname;
  bool get isLoading => _isLoading;
  bool get courseChanged => _courseChanged;

  UserProvider() {
    loadFromPrefs();
  }

  void setUser(int id, String token, {String? avatarUrl, String? nickname}) {
    AppLogger.info('设置用户: id=$id, token=$token, avatarUrl=$avatarUrl, nickname=$nickname');
    _userId = id;
    _token = token;
    _avatarUrl = avatarUrl;
    _nickname = nickname;
    _saveToPrefs();
    notifyListeners();
  }

  void setAvatarUrl(String url) {
    AppLogger.info('设置用户头像: $url');
    _avatarUrl = url;
    _saveToPrefs();
    notifyListeners();
  }

  void setNickname(String nickname) {
    AppLogger.info('设置用户昵称: $nickname');
    _nickname = nickname;
    _saveToPrefs();
    notifyListeners();
  }

  Future<void> loadFromPrefs() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      _userId = prefs.getInt('userId') ?? 0;
      _token = prefs.getString('token');
      _avatarUrl = prefs.getString('avatarUrl');
      _nickname = prefs.getString('nickname');

      AppLogger.info('从本地加载用户信息: userId=$_userId, token=$_token, avatarUrl=$_avatarUrl, nickname=$_nickname');

      if (_userId != 0 && (_token?.isNotEmpty ?? false)) {
        AppLogger.info('本地存在登录状态');
      } else {
        AppLogger.info('本地无登录状态');
      }
    } catch (e) {
      AppLogger.error('加载用户信息错误', error: e);
      _userId = 0;
      _token = null;
      _avatarUrl = null;
      _nickname = null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearUser() async {
    AppLogger.info('清除用户信息');
    _userId = 0;
    _token = null;
    _avatarUrl = null;
    _nickname = null;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('userId');
      await prefs.remove('token');
      await prefs.remove('avatarUrl');
      await prefs.remove('nickname');
      AppLogger.info('已从本地存储中移除用户信息');
    } catch (e) {
      AppLogger.error('清除用户信息错误', error: e);
    }

    notifyListeners();
  }

  Future<void> _saveToPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('userId', _userId);
      await prefs.setString('token', _token ?? '');
      if (_avatarUrl != null) {
        await prefs.setString('avatarUrl', _avatarUrl!);
      }
      if (_nickname != null) {
        await prefs.setString('nickname', _nickname!);
      }
      AppLogger.success('用户信息已保存到本地存储');
    } catch (e) {
      AppLogger.error('保存用户信息错误', error: e);
    }
  }

  void notifyCourseChange() {
    _courseChanged = true;
    notifyListeners();
    AppLogger.info('已通知课程数据变化');
  }

  void resetCourseChanged() {
    _courseChanged = false;
    AppLogger.info('重置课程数据变化标志');
  }

  bool get isLoggedIn => _userId != 0 && (_token?.isNotEmpty ?? false);
}
