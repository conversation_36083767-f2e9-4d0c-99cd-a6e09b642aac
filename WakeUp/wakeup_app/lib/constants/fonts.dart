import 'dart:io';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

/// 应用程序字体常量类
class AppFonts {
  // 标准字间距值
  static const double standardLetterSpacing = 0.5;
  static const double titleLetterSpacing = 0.5; // 普通标题字间距
  static const double compactTitleLetterSpacing = -0.5; // 紧凑标题字间距
  static const double labelLetterSpacing = 0.6;

  /// 获取适合当前平台的中文字体家族
  static String get platformChineseFont {
    if (Platform.isIOS || Platform.isMacOS) {
      return 'PingFang SC'; // iOS/macOS 使用苹方字体
    } else {
      return 'Noto Sans SC'; // Android 使用思源黑体
    }
  }

  /// 获取适合当前平台的拉丁字体家族（英文、数字等）
  static String get platformLatinFont {
    if (Platform.isIOS || Platform.isMacOS) {
      return '.SF Pro Text'; // iOS/macOS 使用 San Francisco 字体 - 修正iOS字体名称
    } else {
      return 'Roboto'; // Android 使用 Roboto 字体
    }
  }

  /// 获取适合当前平台的拉丁字体家族的显示版本（用于标题等）
  static String get platformLatinDisplayFont {
    if (Platform.isIOS || Platform.isMacOS) {
      return '.SFProDisplay-Black'; // iOS使用SF Pro Display Black
    } else {
      return 'Roboto'; // Android 使用 Roboto 字体
    }
  }

  /// 创建适合当前平台的字体配置
  static TextTheme createPlatformTextTheme(TextTheme baseTheme) {
    return baseTheme.apply(
      fontFamily: platformChineseFont,
      displayColor: Colors.white,
      bodyColor: Colors.white,
    );
  }

  /// 创建混合字体样式 - 自动适应平台
  /// 使用fontFamilyFallback和fontFeatures确保中文和英文数字分别使用最合适的字体
  static TextStyle createMixedStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
    double? letterSpacing,
    bool useCompactSpacing = false,
    bool isTitle = false,
  }) {
    // 为标题应用紧凑字间距
    double effectiveLetterSpacing =
        letterSpacing ??
        (isTitle
            ? (useCompactSpacing
                ? compactTitleLetterSpacing
                : titleLetterSpacing)
            : standardLetterSpacing);

    return TextStyle(
      // 中文字符的主要字体
      fontFamily: platformChineseFont,
      fontSize: fontSize,
      fontWeight: fontWeight ?? (isTitle ? FontWeight.w900 : FontWeight.normal),
      color: color,
      height: height ?? (isTitle ? 0.95 : null),
      decoration: decoration,
      fontStyle: fontStyle,
      letterSpacing: effectiveLetterSpacing,
      // 使用fontFamilyFallback保证拉丁字符(英文、数字)会使用对应平台的拉丁字体
      fontFamilyFallback: [
        isTitle ? platformLatinDisplayFont : platformLatinFont,
      ],
      // 使用fontFeatures让Text widget能够智能地为中英文分别选择合适的字体
      fontFeatures: [
        const ui.FontFeature.alternativeFractions(),
        const ui.FontFeature.contextualAlternates(),
      ],
    );
  }

  /// 创建纯拉丁字体样式(英文、数字) - 自动适应平台
  static TextStyle createLatinStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    TextDecoration? decoration,
    FontStyle? fontStyle,
    double? letterSpacing,
    bool isDisplayFont = false,
    bool useCompactSpacing = false,
    bool isTitle = false,
  }) {
    // 为标题应用紧凑字间距
    double effectiveLetterSpacing =
        letterSpacing ??
        (isTitle
            ? (useCompactSpacing
                ? compactTitleLetterSpacing
                : titleLetterSpacing)
            : standardLetterSpacing);

    return TextStyle(
      // 以拉丁字体为主要字体 - 根据需要选择Text或Display版本
      fontFamily:
          isDisplayFont || isTitle
              ? platformLatinDisplayFont
              : platformLatinFont,
      fontSize: fontSize,
      fontWeight: fontWeight ?? (isTitle ? FontWeight.w900 : FontWeight.w400),
      color: color,
      height: height ?? (isTitle ? 0.95 : null),
      decoration: decoration,
      fontStyle: fontStyle,
      letterSpacing: effectiveLetterSpacing,
      // 如果有非拉丁字符则回退到中文字体
      fontFamilyFallback: [platformChineseFont],
    );
  }

  /// 创建页面标题黑体样式 - 自动适应平台
  /// 自动使用平台适配的黑体字体并添加阴影效果增强视觉表现
  static TextStyle createTitleStyle({
    double fontSize = 36,
    Color color = Colors.white,
    double? letterSpacing,
    double? height,
    bool withShadow = true,
    bool isLatinText = false,
    bool useCompactSpacing = true, // 默认使用紧凑字间距
  }) {
    // 计算最终使用的字间距
    double effectiveLetterSpacing =
        letterSpacing ??
        (useCompactSpacing ? compactTitleLetterSpacing : titleLetterSpacing);

    // 为iOS创建特殊的黑体样式
    if ((Platform.isIOS || Platform.isMacOS) && isLatinText) {
      // 创建基础文本样式
      TextStyle baseStyle = TextStyle(
        fontFamily: '.SFProDisplay-Black', // 使用Black字重
        fontSize: fontSize * 1.05, // 稍微放大字体
        fontWeight: FontWeight.w900,
        color: color,
        letterSpacing: effectiveLetterSpacing,
        height: height ?? 0.95,
        fontFeatures: [
          ui.FontFeature.stylisticSet(1),
          ui.FontFeature.stylisticSet(2),
          ui.FontFeature.enable('kern'),
        ],
      );

      // 添加阴影和描边效果
      if (withShadow) {
        return baseStyle.copyWith(
          shadows: [
            // 深色描边效果
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(0.6, 0.6),
              blurRadius: 0,
            ),
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(-0.6, 0.6),
              blurRadius: 0,
            ),
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(0.6, -0.6),
              blurRadius: 0,
            ),
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(-0.6, -0.6),
              blurRadius: 0,
            ),

            // 额外的阴影增强边缘
            Shadow(
              color: Colors.black.withValues(alpha: 0.6),
              offset: const Offset(1, 0),
              blurRadius: 0,
            ),
            Shadow(
              color: Colors.black.withValues(alpha: 0.6),
              offset: const Offset(-1, 0),
              blurRadius: 0,
            ),
            Shadow(
              color: Colors.black.withValues(alpha: 0.6),
              offset: const Offset(0, 1),
              blurRadius: 0,
            ),
            Shadow(
              color: Colors.black.withValues(alpha: 0.6),
              offset: const Offset(0, -1),
              blurRadius: 0,
            ),

            // 主阴影效果
            Shadow(
              color: Colors.black.withValues(alpha: 0.7),
              offset: const Offset(0, 1.5),
              blurRadius: 0.5,
            ),
          ],
        );
      }

      return baseStyle;
    }

    // 非iOS设备或非拉丁文本
    // 根据平台和语言自动选择合适的样式
    final baseStyle =
        isLatinText
            ? createLatinStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w900,
              color: color,
              letterSpacing: effectiveLetterSpacing,
              height: height ?? 0.95,
              isDisplayFont: true,
              isTitle: true,
            )
            : createMixedStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w900,
              color: color,
              letterSpacing: effectiveLetterSpacing,
              height: height ?? 0.95,
              isTitle: true,
            );

    if (!withShadow) {
      return baseStyle;
    }

    // 为Android平台也添加增强的阴影效果，更接近iOS效果
    if (Platform.isAndroid) {
      return baseStyle.copyWith(
        shadows: [
          Shadow(
            color: Colors.black.withValues(alpha: 0.7),
            offset: const Offset(0.4, 0.4),
            blurRadius: 0,
          ),
          Shadow(
            color: Colors.black.withValues(alpha: 0.7),
            offset: const Offset(-0.4, 0.4),
            blurRadius: 0,
          ),
          Shadow(
            color: Colors.black.withValues(alpha: 0.7),
            offset: const Offset(0.4, -0.4),
            blurRadius: 0,
          ),
          Shadow(
            color: Colors.black.withValues(alpha: 0.7),
            offset: const Offset(-0.4, -0.4),
            blurRadius: 0,
          ),
          Shadow(
            color: Colors.black.withValues(alpha: 0.6),
            offset: const Offset(0, 1.2),
            blurRadius: 0.4,
          ),
        ],
      );
    }

    // 通用阴影效果
    return baseStyle.copyWith(
      shadows: [
        Shadow(
          color: Colors.black.withValues(alpha: 0.5),
          offset: const Offset(0, 1),
          blurRadius: 1,
        ),
        Shadow(
          color: Colors.white.withValues(alpha: 0.2),
          offset: const Offset(0, -0.5),
          blurRadius: 0,
        ),
      ],
    );
  }

  /// 创建粗体文字Widget，使用堆叠多个微调位置的Text实现超粗的文字效果
  /// 注意：这替代了直接使用TextStyle的方法，应该在Text外部使用
  /// 自动适应平台，在iOS上使用SF字体，在Android上使用Roboto字体
  static Widget createBoldTextStack(
    String text, {
    double fontSize = 36,
    Color color = Colors.white,
    double? letterSpacing,
    double? height,
    TextAlign textAlign = TextAlign.center,
    bool useCompactSpacing = true, // 默认使用紧凑字间距
  }) {
    // 计算最终使用的字间距
    double effectiveLetterSpacing =
        letterSpacing ??
        (useCompactSpacing ? compactTitleLetterSpacing : titleLetterSpacing);

    // 根据平台选择适当的字体家族
    String fontFamily =
        Platform.isIOS || Platform.isMacOS ? '.SFProDisplay-Black' : 'Roboto';

    // 文字样式 - 根据平台调整
    TextStyle baseStyle = TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: FontWeight.w900,
      color: color,
      letterSpacing: effectiveLetterSpacing,
      height: height ?? 0.95,
    );

    // 创建主文本和背景文本的堆叠效果
    return Stack(
      alignment: Alignment.centerLeft,
      children: [
        // 背景阴影文本 - 为所有平台统一添加描边效果
        Text(
          text,
          style: baseStyle.copyWith(
            color: Colors.black.withValues(alpha: 0.8),
            shadows:
                Platform.isAndroid
                    ? [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.7),
                        offset: const Offset(0.4, 0.4),
                        blurRadius: 0,
                      ),
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.7),
                        offset: const Offset(-0.4, 0.4),
                        blurRadius: 0,
                      ),
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.6),
                        offset: const Offset(0, 1.2),
                        blurRadius: 0.4,
                      ),
                    ]
                    : null,
          ),
          textAlign: TextAlign.left,
        ),

        // 主文本
        Text(
          text,
          style: baseStyle.copyWith(
            shadows:
                Platform.isAndroid
                    ? [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.5),
                        offset: const Offset(0, 0.8),
                        blurRadius: 0.3,
                      ),
                    ]
                    : null,
          ),
          textAlign: TextAlign.left,
        ),
      ],
    );
  }

  /// 标题文本样式 - 自动适应平台
  static TextStyle get headingStyle => createMixedStyle(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    letterSpacing: standardLetterSpacing,
    isTitle: true,
  );

  /// 副标题文本样式 - 自动适应平台
  static TextStyle get subheadingStyle => createMixedStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: Colors.white,
    letterSpacing: standardLetterSpacing,
  );

  /// 正文文本样式 - 自动适应平台
  static TextStyle get bodyStyle => createMixedStyle(
    fontSize: 14,
    color: Colors.white,
    letterSpacing: standardLetterSpacing,
  );

  /// 小字体文本样式 - 自动适应平台
  static TextStyle get captionStyle => createMixedStyle(
    fontSize: 12,
    color: Colors.white70,
    letterSpacing: standardLetterSpacing,
  );
}
