import 'package:flutter/material.dart';

class AppColors {
  // 黑色系列
  static const Color black = Color(0xFF0E0E0E);
  static const Color deepBlack = Color(0xFF000000);
  static const Color charcoal = Color(0xFF222222);
  static const Color darkGray = Color(0xFF333333);

  // 灰色系列
  static const Color mediumGray = Color(0xFF666666);
  static const Color gray = Color(0xFF888888);
  static const Color grayLight = Color(0xFFAAAAAA);
  static const Color lightGray = Color(0xFFCCCCCC);

  // 白色系列
  static const Color offWhite = Color(0xFFF5F5F5);
  static const Color white = Color(0xFFFFFFFF);

  // 强调色（保持为黑白色调）
  static const Color accentDark = Color(0xFF303030);
  static const Color accentLight = Color(0xFFE0E0E0);

  // 黑白渐变
  static const LinearGradient mainGradient = LinearGradient(
    colors: [black, darkGray],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // 灰度渐变
  static const LinearGradient grayGradient = LinearGradient(
    colors: [darkGray, lightGray],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
