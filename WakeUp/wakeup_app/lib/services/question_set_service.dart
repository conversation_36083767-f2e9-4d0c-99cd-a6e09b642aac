import 'api_service.dart';
import 'user_service.dart';

/// 问题集服务类，处理获取问题集和问题相关的API请求
class QuestionSetService extends ApiService {
  /// 单例模式
  static final QuestionSetService _instance = QuestionSetService._internal();
  
  factory QuestionSetService() {
    return _instance;
  }
  
  QuestionSetService._internal();
  
  /// 用户服务实例
  final UserService _userService = UserService();
  
  /// 获取课程下的所有问题集
  /// [courseId] 课程ID
  Future<List<Map<String, dynamic>>> getQuestionSets(int courseId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/question_sets', 
      token: token,
      queryParams: {
        'course_id': courseId.toString(),
      },
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取特定类别下的问题集
  /// [courseId] 课程ID
  /// [level4Id] 四级类别ID，可选
  /// [level5Id] 五级类别ID，可选
  Future<List<Map<String, dynamic>>> getQuestionSetsByCategory({
    required int courseId,
    int? level4Id,
    int? level5Id,
  }) async {
    final token = await _userService.getAuthToken();
    final queryParams = {
      'course_id': courseId.toString(),
    };
    
    if (level4Id != null) {
      queryParams['category_level4_id'] = level4Id.toString();
    }
    
    if (level5Id != null) {
      queryParams['category_level5_id'] = level5Id.toString();
    }
    
    final response = await get('/question_sets', 
      token: token,
      queryParams: queryParams,
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取问题集详情
  /// [questionSetId] 问题集ID
  Future<Map<String, dynamic>> getQuestionSetDetails(int questionSetId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/question_sets/$questionSetId', 
      token: token,
      expectList: false
    );
    return Map<String, dynamic>.from(response);
  }
  
  /// 获取问题集中的所有问题
  /// [questionSetId] 问题集ID
  Future<List<Map<String, dynamic>>> getQuestionsBySet(int questionSetId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/questions', 
      token: token,
      queryParams: {
        'question_set_id': questionSetId.toString(),
      },
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取课程中的所有问题
  /// [courseId] 课程ID
  /// [questionType] 问题类型，可选
  /// [difficulty] 难度级别，可选
  Future<List<Map<String, dynamic>>> getQuestionsByCourse({
    required int courseId,
    String? questionType,
    String? difficulty,
  }) async {
    final token = await _userService.getAuthToken();
    final queryParams = {
      'course_id': courseId.toString(),
    };
    
    if (questionType != null) {
      queryParams['question_type'] = questionType;
    }
    
    if (difficulty != null) {
      queryParams['difficulty'] = difficulty;
    }
    
    final response = await get('/questions', 
      token: token,
      queryParams: queryParams,
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 生成自定义测验
  /// [courseId] 课程ID
  /// [questionCount] 问题数量
  /// [questionTypes] 问题类型列表
  /// [difficulty] 难度，可选
  Future<List<Map<String, dynamic>>> generateQuiz({
    required int courseId,
    required int questionCount,
    required List<String> questionTypes,
    String? difficulty,
  }) async {
    final token = await _userService.getAuthToken();
    final Map<String, dynamic> data = {
      'course_id': courseId,
      'count': questionCount,
      'question_types': questionTypes,
    };
    
    if (difficulty != null) {
      data['difficulty'] = difficulty;
    }
    
    final response = await post('/generate_quiz', 
      token: token,
      body: data,
      expectList: true
    );
    return List<Map<String, dynamic>>.from(response);
  }
  
  /// 获取问题详情
  /// [questionId] 问题ID
  Future<Map<String, dynamic>> getQuestionDetails(int questionId) async {
    final token = await _userService.getAuthToken();
    final response = await get('/questions/$questionId', 
      token: token,
      expectList: false
    );
    return Map<String, dynamic>.from(response);
  }
} 