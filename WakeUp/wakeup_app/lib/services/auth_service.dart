import 'dart:convert';
import 'package:http/http.dart' as http;
import 'dart:io' show Platform;
import 'dart:async';
import 'api_service.dart';
import '../core/services/storage_service.dart';
import '../core/config/api_endpoints.dart';
import 'package:wakeup_app/core/utils/logger.dart';

/// 认证服务类
class AuthService extends ApiService {
  static const String _tokenKey = 'auth_token'; // ignore: unused_field
  static const String _phoneKey = 'user_phone';

  /// 单例模式
  static final AuthService _instance = AuthService._internal();

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  /// 获取保存的令牌
  Future<String?> getToken() async {
    return await StorageService.instance.getAuthToken();
  }

  /// 保存令牌
  Future<void> saveToken(String token) async {
    await StorageService.instance.setAuthToken(token);
  }

  /// 获取保存的手机号
  Future<String?> getPhone() async {
    return await StorageService.instance.getString(_phoneKey);
  }

  /// 保存手机号
  Future<void> savePhone(String phone) async {
    await StorageService.instance.setString(_phoneKey, phone);
  }

  /// 发送验证码
  Future<Map<String, dynamic>> sendVerificationCode(String phone) async {
    return await post('/send_code', body: {'phone': phone}, expectList: false);
  }

  /// 登录
  Future<Map<String, dynamic>> login(String phone, String code) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'phone': phone, 'code': code}),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);

        // 检查是否有错误消息
        if (jsonData.containsKey('message') &&
            (response.statusCode >= 400 ||
                jsonData['message'].toString().contains('错误'))) {
          return {'success': false, 'message': jsonData['message'] ?? '未知错误'};
        }

        // 检查正确的响应格式（包含 user_id 和 token）
        if (jsonData.containsKey('user_id') && jsonData.containsKey('token')) {
          final userId = jsonData['user_id'];
          final token = jsonData['token'];

          // 保存登录信息
          await saveToken(token);
          await savePhone(phone);

          return {
            'success': true,
            'message': '登录成功',
            'user_id': userId,
            'token': token,
          };
        }

        // 未知的响应格式
        return {'success': false, 'message': '服务器返回格式异常'};
      } else {
        // 处理非200响应
        try {
          final errorData = json.decode(response.body);
          return {
            'success': false,
            'message': errorData['message'] ?? '服务器错误: ${response.statusCode}',
          };
        } catch (e) {
          return {'success': false, 'message': '服务器错误: ${response.statusCode}'};
        }
      }
    } catch (e) {
      AppLogger.error('登录请求异常', error: e);
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 登出
  Future<Map<String, dynamic>> logout() async {
    final token = await getToken();
    final response = await post('/user/logout', token: token);

    if (response['code'] == 0) {
      // Using StorageService instead
      await StorageService.instance.clearAuthToken();
    }

    return response;
  }

  /// 检查是否已认证
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// 清除身份验证数据
  Future<void> clearAuthData() async {
    // Using StorageService instead
    await StorageService.instance.clearAuthToken();
    await StorageService.instance.remove(_phoneKey);
  }

  /// API基础URL - 根据平台选择正确的地址
  static String get baseUrl {
    if (Platform.isAndroid) {
      return 'http://********:5001'; // Android模拟器访问宿主机
    } else if (Platform.isIOS) {
      // iOS设备访问宿主机本地服务器（临时测试用）
      return 'http://************:5001';
    } else {
      return 'http://127.0.0.1:5001'; // 开发机直接访问
    }
  }

  /// 备用API基础URL列表 - 用于网络连接失败时的回退
  static List<String> get fallbackUrls {
    if (Platform.isAndroid) {
      return [
        'http://********:5001',
        'http://127.0.0.1:5001',
        'http://************:5001',
      ];
    } else if (Platform.isIOS) {
      return [
        'http://************:5001',
        'http://localhost:5001',
        'http://127.0.0.1:5001',
        'https://wakeup-api.loca.lt',
      ];
    } else {
      return [
        'http://127.0.0.1:5001',
        'http://localhost:5001',
        'http://************:5001',
      ];
    }
  }

  /// 用户认证令牌
  static String? _token;

  /// 获取用户认证令牌
  static String get token => _token ?? '';

  /// 设置用户认证令牌
  static set token(String value) {
    _token = value;
  }

  /// 初始化认证服务，从本地存储加载token
  static Future<void> initialize() async {
    // Using StorageService instead
    _token = await StorageService.instance.getAuthToken();
  }

  /// 保存令牌到本地存储
  static Future<void> saveTokenStatic(String token) async {
    _token = token;
    // Using StorageService instead
    await StorageService.instance.setAuthToken(token);
  }

  /// 清除令牌
  static Future<void> clearToken() async {
    _token = null;
    // Using StorageService instead
    await StorageService.instance.clearAuthToken();
  }

  // 开发模式标志 - 但仍然执行真实请求
  static bool isDebugMode = true;

  /// 使用账号密码登录
  static Future<Map<String, dynamic>> loginWithPassword(
    String account,
    String password,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'account': account, 'password': password}),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);

        // 检查是否有错误消息
        if (jsonData.containsKey('message') &&
            (response.statusCode >= 400 ||
                jsonData['message'].toString().contains('错误'))) {
          return {'success': false, 'message': jsonData['message'] ?? '未知错误'};
        }

        // 检查正确的响应格式（包含 user_id 和 token）
        if (jsonData.containsKey('user_id') && jsonData.containsKey('token')) {
          final userId = jsonData['user_id'];
          final token = jsonData['token'];

          // 保存登录信息
          await saveTokenStatic(token);
          // Using StorageService instead
          await StorageService.instance.setString(_phoneKey, account);

          return {
            'success': true,
            'message': '登录成功',
            'user_id': userId,
            'token': token,
          };
        }

        // 未知的响应格式
        return {'success': false, 'message': '服务器返回格式异常'};
      } else {
        // 处理非200响应
        try {
          final errorData = json.decode(response.body);
          return {
            'success': false,
            'message': errorData['message'] ?? '服务器错误: ${response.statusCode}',
          };
        } catch (e) {
          return {'success': false, 'message': '服务器错误: ${response.statusCode}'};
        }
      }
    } catch (e) {
      AppLogger.error('登录请求异常', error: e);
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 检查用户是否已登录
  static bool isLoggedIn() {
    return _token != null && _token!.isNotEmpty;
  }

  static Future<bool> sendCode(String phone) async {
    try {
      if (isDebugMode) {
        AppLogger.info('调用发送验证码 API: $baseUrl${ApiEndpoints.auth.sendCode}');
        AppLogger.info('手机号: $phone');
      }

      final res = await http.post(
        Uri.parse('$baseUrl${ApiEndpoints.auth.sendCode}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'phone': phone}),
      );

      if (isDebugMode) {
        AppLogger.info('发送验证码响应码: ${res.statusCode}');
        AppLogger.info('发送验证码响应体: ${res.body}');
      }

      return res.statusCode == 200;
    } catch (e) {
      AppLogger.error('发送验证码错误', error: e);
      return false;
    }
  }

  // 发送验证码并尝试从响应中获取验证码值（适用于开发阶段）
  static Future<Map<String, dynamic>> sendCodeAndGetValue(String phone) async {
    try {
      if (isDebugMode) {
        AppLogger.info('调用发送验证码 API: $baseUrl${ApiEndpoints.auth.sendCode}');
        AppLogger.info('手机号: $phone');
      }

      final res = await http.post(
        Uri.parse('$baseUrl${ApiEndpoints.auth.sendCode}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'phone': phone}),
      );

      if (isDebugMode) {
        AppLogger.info('发送验证码响应码: ${res.statusCode}');
        AppLogger.info('发送验证码响应体: ${res.body}');
      }

      if (res.statusCode == 200) {
        try {
          final data = jsonDecode(res.body);
          final message = data['message'] as String? ?? '';

          // 直接从响应中获取验证码
          //
          // 直接从响应中获取验证码
          final code = data["code"] as String?;
          if (code != null) {
            // code已经获取
            if (isDebugMode) {
              AppLogger.success('从响应中获取到验证码: $code');
            }
          }

          return {'success': true, 'message': message, 'code': code};
        } catch (e) {
          AppLogger.error('解析验证码响应错误', error: e);
          return {'success': true, 'message': '验证码已发送'};
        }
      } else {
        return {'success': false, 'message': '验证码发送失败: ${res.statusCode}'};
      }
    } catch (e) {
      AppLogger.error('发送验证码网络错误', error: e);
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  static Future<Map<String, dynamic>> getUserInfo(
    int userId,
    String token,
  ) async {
    try {
      final res = await http.get(
        Uri.parse('$baseUrl/api/user/$userId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (res.statusCode == 200) {
        return jsonDecode(res.body);
      } else {
        // 如果API尚未实现，返回模拟数据
        return {
          'name': '用户$userId',
          'nickname': '用户昵称$userId',
          'username': 'user$userId',
          'phone': '138****${userId.toString().padLeft(4, '0')}',
          'avatar': null,
          'gender': '男',
          'region': '中国大陆',
          'course_count': 2,
          'completed_count': 1,
          'study_hours': 12,
        };
      }
    } catch (e) {
      AppLogger.error('获取用户信息错误', error: e);
      return {
        'name': '用户$userId',
        'nickname': '用户昵称$userId',
        'username': 'user$userId',
        'phone': '138****${userId.toString().padLeft(4, '0')}',
        'avatar': null,
        'gender': '',
        'region': '',
        'course_count': 0,
        'completed_count': 0,
        'study_hours': 0,
      };
    }
  }

  /// 检查令牌是否有效
  static Future<bool> verifyToken(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/verify_token'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      AppLogger.error('验证令牌错误', error: e);
      return false;
    }
  }

  /// 登录页面初始化时检查令牌有效性
  static Future<bool> checkTokenOnStartup() async {
    // Using StorageService instead
    final token = await StorageService.instance.getAuthToken();

    if (token == null || token.isEmpty) {
      return false;
    }

    // 保存到静态变量
    _token = token;

    // 验证令牌有效性
    return await verifyToken(token);
  }

  /// 用户注册
  static Future<Map<String, dynamic>> register(
    Map<String, dynamic> userData,
  ) async {
    try {
      AppLogger.info('注册请求URL: $baseUrl/api/auth/register');
      AppLogger.info('注册请求数据: ${jsonEncode(userData)}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(userData),
      );

      AppLogger.info('注册响应状态码: ${response.statusCode}');
      AppLogger.info('注册响应体: ${response.body}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);

        // 检查是否有错误消息
        if (jsonData.containsKey('message') &&
            (response.statusCode >= 400 ||
                jsonData['message'].toString().contains('错误'))) {
          return {'success': false, 'message': jsonData['message'] ?? '未知错误'};
        }

        // 检查正确的响应格式（包含用户ID和token）
        if (jsonData.containsKey('user_id') && jsonData.containsKey('token')) {
          final userId = jsonData['user_id'];
          final token = jsonData['token'];

          // 保存登录信息
          await saveTokenStatic(token);

          // 如果有手机号，保存手机号
          if (userData.containsKey('phone') && userData['phone'] != null) {
            // Using StorageService instead
            await StorageService.instance.setString(
              _phoneKey,
              userData['phone'],
            );
          }

          return {
            'success': true,
            'message': '注册成功',
            'user_id': userId,
            'token': token,
          };
        }

        // 如果服务器返回成功但没有提供用户ID和token
        return {'success': true, 'message': '注册成功，请登录'};
      } else {
        // 处理非200响应
        try {
          final errorData = json.decode(response.body);
          return {
            'success': false,
            'message': errorData['message'] ?? '服务器错误: ${response.statusCode}',
          };
        } catch (e) {
          return {'success': false, 'message': '服务器错误: ${response.statusCode}'};
        }
      }
    } catch (e) {
      AppLogger.error('注册请求异常', error: e);
      return {'success': false, 'message': '网络错误: $e'};
    }
  }

  /// 修改账号密码
  static Future<Map<String, dynamic>> changePassword(
    String phone,
    String currentPassword,
    String newPassword,
  ) async {
    // Implementation needed
    throw UnimplementedError();
  }

  /// 检查账号是否已存在
  static Future<Map<String, dynamic>> checkAccountExists(
    String account,
    String accountType,
  ) async {
    try {
      AppLogger.info('检查账号是否存在: $account, 类型: $accountType');

      final response = await http.post(
        Uri.parse('$baseUrl/api/auth/check-account'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'account': account, 'type': accountType}),
      );

      AppLogger.info('检查账号响应状态码: ${response.statusCode}');
      AppLogger.info('检查账号响应体: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final exists = jsonData['exists'] as bool? ?? false;
        final type = jsonData['account_type'] as String?;

        return {
          'success': true,
          'exists': exists,
          'account_type': type,
          'message': exists ? '账号已存在' : '账号可用',
        };
      } else {
        try {
          final errorData = json.decode(response.body);
          return {
            'success': false,
            'exists': false,
            'message': errorData['message'] ?? '服务器错误: ${response.statusCode}',
          };
        } catch (e) {
          return {
            'success': false,
            'exists': false,
            'message': '服务器错误: ${response.statusCode}',
          };
        }
      }
    } catch (e) {
      AppLogger.error('检查账号请求异常', error: e);
      return {'success': false, 'exists': false, 'message': '网络错误: $e'};
    }
  }
}
