import 'dart:convert';
import '../../services/auth_service.dart';
import 'package:http/http.dart' as http;
import '../utils/app_exceptions.dart';

/// 统一的API客户端服务
/// 解决了原项目中16个文件重复HTTP调用的问题
class ApiClient {
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;
  ApiClient._internal();

  static const int _timeoutSeconds = 30;
  final http.Client _httpClient = http.Client();

  /// 基础URL（复用 AuthService 的平台自适配地址）
  static String get baseUrl => AuthService.baseUrl;

  /// 统一的请求头
  Map<String, String> _getHeaders({String? token}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// GET请求
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    String? token,
    T Function(dynamic)? parser,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint').replace(
        queryParameters: queryParams?.map((k, v) => MapEntry(k, v.toString())),
      );

      final response = await _httpClient
          .get(uri, headers: _getHeaders(token: token))
          .timeout(Duration(seconds: _timeoutSeconds));

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return ApiResponse.error(_handleException(e));
    }
  }

  /// POST请求
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    String? token,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl$endpoint'),
            headers: _getHeaders(token: token),
            body: body != null ? json.encode(body) : null,
          )
          .timeout(Duration(seconds: _timeoutSeconds));

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return ApiResponse.error(_handleException(e));
    }
  }

  /// PUT请求
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    String? token,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _httpClient
          .put(
            Uri.parse('$baseUrl$endpoint'),
            headers: _getHeaders(token: token),
            body: body != null ? json.encode(body) : null,
          )
          .timeout(Duration(seconds: _timeoutSeconds));

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return ApiResponse.error(_handleException(e));
    }
  }

  /// DELETE请求
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    String? token,
    T Function(dynamic)? parser,
  }) async {
    try {
      final response = await _httpClient
          .delete(
            Uri.parse('$baseUrl$endpoint'),
            headers: _getHeaders(token: token),
          )
          .timeout(Duration(seconds: _timeoutSeconds));

      return _handleResponse<T>(response, parser);
    } catch (e) {
      return ApiResponse.error(_handleException(e));
    }
  }

  /// 统一的响应处理
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? parser,
  ) {
    try {
      final dynamic data = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (parser != null) {
          return ApiResponse.success(parser(data));
        } else {
          return ApiResponse.success(data as T);
        }
      } else {
        final errorMessage =
            data is Map && data.containsKey('error')
                ? data['error'].toString()
                : 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
        return ApiResponse.error(
          ApiException(errorMessage, response.statusCode),
        );
      }
    } catch (e) {
      return ApiResponse.error(ApiException('解析响应失败: $e', response.statusCode));
    }
  }

  /// 统一的异常处理
  AppException _handleException(dynamic error) {
    if (error is http.ClientException) {
      return NetworkException('网络连接失败，请检查网络设置');
    } else if (error.toString().contains('TimeoutException')) {
      return TimeoutException('请求超时，请稍后重试');
    } else {
      return UnknownException('未知错误: ${error.toString()}');
    }
  }

  /// 释放资源
  void dispose() {
    _httpClient.close();
  }
}

/// API响应包装类
class ApiResponse<T> {
  final T? data;
  final AppException? error;
  final bool isSuccess;

  ApiResponse._({this.data, this.error, required this.isSuccess});

  factory ApiResponse.success(T data) =>
      ApiResponse._(data: data, isSuccess: true);

  factory ApiResponse.error(AppException error) =>
      ApiResponse._(error: error, isSuccess: false);

  /// 是否有错误
  bool get hasError => error != null;

  /// 获取数据或抛出异常
  T get dataOrThrow {
    if (hasError) throw error!;
    return data!;
  }
}
