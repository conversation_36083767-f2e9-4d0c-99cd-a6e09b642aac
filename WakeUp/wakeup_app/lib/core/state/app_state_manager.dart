import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';

/// 应用全局状态管理器
/// 提供统一的状态管理和持久化能力
class AppStateManager extends ChangeNotifier {
  static AppStateManager? _instance;
  static AppStateManager get instance => _instance ??= AppStateManager._();
  
  AppStateManager._();

  // 应用状态
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _error;
  
  // 用户状态
  bool _isLoggedIn = false;
  Map<String, dynamic>? _userInfo;
  String? _authToken;
  
  // 应用设置
  String _language = 'zh';
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _isLoggedIn;
  Map<String, dynamic>? get userInfo => _userInfo;
  String? get authToken => _authToken;
  String get language => _language;
  bool get isDarkMode => _isDarkMode;
  bool get notificationsEnabled => _notificationsEnabled;

  /// 初始化应用状态
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _setLoading(true);
    try {
      // 从本地存储恢复状态
      await _loadPersistedState();
      _isInitialized = true;
    } catch (e) {
      _setError('初始化失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 从本地存储加载持久化状态
  Future<void> _loadPersistedState() async {
    final storage = StorageService.instance;
    
    // 加载用户状态
    _authToken = await storage.getAuthToken();
    if (_authToken != null) {
      _userInfo = await storage.getUserInfo();
      _isLoggedIn = _userInfo != null;
    }
    
    // 加载应用设置
    _language = await storage.getString('language') ?? 'zh';
    _isDarkMode = await storage.getBool('dark_mode') ?? false;
    _notificationsEnabled = await storage.getBool('notifications_enabled') ?? true;
  }

  /// 设置用户登录状态
  Future<void> setUserLogin({
    required String token,
    required Map<String, dynamic> userInfo,
  }) async {
    _authToken = token;
    _userInfo = userInfo;
    _isLoggedIn = true;
    
    // 持久化到本地存储
    final storage = StorageService.instance;
    await storage.setAuthToken(token);
    await storage.setUserInfo(userInfo);
    
    _clearError();
    notifyListeners();
  }

  /// 用户登出
  Future<void> logout() async {
    _authToken = null;
    _userInfo = null;
    _isLoggedIn = false;
    
    // 清除本地存储
    final storage = StorageService.instance;
    await storage.clear();
    
    notifyListeners();
  }

  /// 更新用户信息
  Future<void> updateUserInfo(Map<String, dynamic> userInfo) async {
    _userInfo = userInfo;
    
    // 持久化到本地存储
    final storage = StorageService.instance;
    await storage.setUserInfo(userInfo);
    
    notifyListeners();
  }

  /// 设置语言
  Future<void> setLanguage(String language) async {
    if (_language != language) {
      _language = language;
      
      // 持久化到本地存储
      final storage = StorageService.instance;
      await storage.setString('language', language);
      
      notifyListeners();
    }
  }

  /// 设置暗黑模式
  Future<void> setDarkMode(bool isDarkMode) async {
    if (_isDarkMode != isDarkMode) {
      _isDarkMode = isDarkMode;
      
      // 持久化到本地存储
      final storage = StorageService.instance;
      await storage.setBool('dark_mode', isDarkMode);
      
      notifyListeners();
    }
  }

  /// 设置通知开关
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled != enabled) {
      _notificationsEnabled = enabled;
      
      // 持久化到本地存储
      final storage = StorageService.instance;
      await storage.setBool('notifications_enabled', enabled);
      
      notifyListeners();
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  /// 重置应用状态
  void reset() {
    _isInitialized = false;
    _isLoading = false;
    _error = null;
    _isLoggedIn = false;
    _userInfo = null;
    _authToken = null;
    _language = 'zh';
    _isDarkMode = false;
    _notificationsEnabled = true;
    notifyListeners();
  }

  @override
  void dispose() {
    _instance = null;
    super.dispose();
  }
}