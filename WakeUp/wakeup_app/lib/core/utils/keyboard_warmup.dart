import 'dart:io' show Platform;
import 'package:flutter/material.dart';

/// 全局键盘预热工具
///
/// 说明：
/// - soft 模式：仅在 Flutter 侧准备 TextInput/Focus 相关对象，不主动弹出系统键盘。
///   对 iOS 首次弹出键盘的加速有限，但安全、不闪屏。
/// - aggressive 模式（需显式开启）：在首屏后短暂聚焦一个不可见的 TextField 再立刻失焦，
///   以预热系统键盘。可能在 iOS 上出现极短暂的键盘闪动，请谨慎开启。
class KeyboardWarmup {
  KeyboardWarmup._();

  static bool _done = false;

  /// 是否启用激进预热（会尝试触发一次真实的键盘激活）
  static bool enableAggressive = false; // 默认关闭，避免 iOS 闪屏

  /// 对外统一入口：只执行一次
  static Future<void> warmUp(BuildContext context) async {
    if (_done) return;
    _done = true;

    if (enableAggressive) {
      await _warmUpAggressive(context);
    } else {
      await _warmUpSoft();
    }
  }

  /// 软预热：不请求键盘，不可见且不改变 UI
  static Future<void> _warmUpSoft() async {
    // 提前创建/释放常用对象，触发 Flutter 侧相关类的首次加载
    final focus = FocusNode();
    final controller = TextEditingController(text: '');
    // 模拟一次生命周期
    focus.dispose();
    controller.dispose();
  }

  /// 激进预热：插入不可见 TextField，短暂 requestFocus -> unfocus
  static Future<void> _warmUpAggressive(BuildContext context) async {
    // 仅对 iOS 生效意义更大；其他平台同样可用
    final overlay = Overlay.maybeOf(context, rootOverlay: true);
    if (overlay == null) return _warmUpSoft();

    final focusNode = FocusNode();
    final controller = TextEditingController();

    final entry = OverlayEntry(
      builder:
          (_) => IgnorePointer(
            child: Opacity(
              opacity: 0.0,
              child: Align(
                alignment: Alignment.bottomCenter,
                child: SizedBox(
                  width: 1,
                  height: 1,
                  child: TextField(
                    focusNode: focusNode,
                    controller: controller,
                    enableInteractiveSelection: false,
                    readOnly: false,
                    showCursor: false,
                    decoration: const InputDecoration(border: InputBorder.none),
                  ),
                ),
              ),
            ),
          ),
    );

    overlay.insert(entry);

    // 等待一帧，确保挂载
    await Future.delayed(const Duration(milliseconds: 16));

    try {
      focusNode.requestFocus();
      // 短暂保持，尽可能触发键盘冷启动加载
      await Future.delayed(const Duration(milliseconds: 160));
      focusNode.unfocus();
    } catch (_) {}

    // 清理
    entry.remove();
    focusNode.dispose();
    controller.dispose();
  }
}

/// 将此组件作为首页内容的外层包装；首次构建后自动进行一次键盘预热
class KeyboardWarmupGate extends StatefulWidget {
  final Widget child;
  const KeyboardWarmupGate({super.key, required this.child});

  @override
  State<KeyboardWarmupGate> createState() => _KeyboardWarmupGateState();
}

class _KeyboardWarmupGateState extends State<KeyboardWarmupGate> {
  @override
  void initState() {
    super.initState();
    // 首帧后预热
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 仅在 iOS 优先考虑；其他平台也可按需启用
      if (Platform.isIOS) {
        KeyboardWarmup.warmUp(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) => widget.child;
}
