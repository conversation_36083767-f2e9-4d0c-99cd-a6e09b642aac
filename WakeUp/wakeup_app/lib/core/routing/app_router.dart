import 'package:flutter/cupertino.dart';
import '../../pages/main/main_page.dart';
import '../../pages/auth/login_page.dart';
import '../../pages/register/register_page.dart';
import '../../pages/admin/admin_course_edit_page.dart';
import '../../pages/quiz/quiz_attempt_page.dart' as quiz_page;
import '../../pages/course/course_page.dart';
import '../../pages/course/course_detail_page.dart';
import '../../pages/resources/resource_library_page.dart';
import '../../pages/common/article_detail_page.dart';
import '../../pages/main/profile_page.dart';
import '../../pages/main/profile_edit_page.dart';
import '../../pages/favorites_page.dart';
import '../../pages/study_history_page.dart';
import '../../pages/wrong_questions_page.dart';
import '../../pages/leaderboard_page.dart';

/// 应用路由管理类
/// 统一管理所有页面路由，提供类型安全的导航
class AppRouter {
  // 路由名称常量
  static const String login = '/login';
  static const String register = '/register';
  static const String main = '/main';
  static const String profile = '/profile';
  static const String profileEdit = '/profile/edit';
  static const String course = '/course';
  static const String courseDetail = '/course/detail';
  static const String quiz = '/quiz';
  static const String favorites = '/favorites';
  static const String studyHistory = '/study-history';
  static const String wrongQuestions = '/wrong-questions';
  static const String leaderboard = '/leaderboard';
  static const String adminCourseEdit = '/admin/course_edit';
  static const String resourceLibrary = '/resource-library';
  static const String articleDetail = '/article-detail';

  /// 获取所有路由配置
  static Map<String, WidgetBuilder> get routes => {
    login: (context) => const LoginPage(),
    register: (context) => const RegisterPage(),
    main: (context) => const MainPage(),
    profile: (context) => const ProfilePage(),
    profileEdit: (context) => const ProfileEditPage(),
    course: (context) => const CoursePage(),
    favorites: (context) => const FavoritesPage(),
    studyHistory: (context) => const StudyHistoryPage(),
    wrongQuestions: (context) => const WrongQuestionsPage(),
    leaderboard: (context) => const LeaderboardPage(),
    adminCourseEdit: (context) => const AdminCourseEditPage(),
    resourceLibrary: (context) => const ResourceLibraryPage(),
  };

  /// 导航到登录页面
  static void toLogin(BuildContext context) {
    Navigator.pushReplacementNamed(context, login);
  }

  /// 导航到注册页面
  static void toRegister(BuildContext context) {
    Navigator.pushNamed(context, register);
  }

  /// 历史栈保留地进入登录页
  static Future<T?> pushLogin<T>(BuildContext context) {
    return Navigator.pushNamed<T>(context, login);
  }

  /// 导航到主页面
  static void toMain(BuildContext context) {
    Navigator.pushReplacementNamed(context, main);
  }

  /// 指定底部导航索引进入主页面（用于从学习页跳转到课程页等场景）
  static void toMainWithIndex(
    BuildContext context, {
    required int initialIndex,
  }) {
    Navigator.pushReplacement(
      context,
      CupertinoPageRoute(builder: (_) => MainPage(initialIndex: initialIndex)),
    );
  }

  /// 导航到个人资料页面
  static void toProfile(BuildContext context) {
    Navigator.pushNamed(context, profile);
  }

  /// 导航到个人资料编辑页面
  static void toProfileEdit(BuildContext context) {
    Navigator.pushNamed(context, profileEdit);
  }

  /// 导航到课程页面
  static void toCourse(BuildContext context) {
    Navigator.pushNamed(context, course);
  }

  /// 导航到课程详情页面
  static void toCourseDetail(
    BuildContext context, {
    required String courseName,
    required String description,
    required String highlight,
    required int categoryId,
  }) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder:
            (context) => CourseDetailPage(
              courseName: courseName,
              description: description,
              highlight: highlight,
              categoryId: categoryId,
            ),
      ),
    );
  }

  /// 导航到资源库页面
  static void toResourceLibrary(
    BuildContext context, {
    int? courseId,
    String? courseName,
  }) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder:
            (context) =>
                ResourceLibraryPage(courseId: courseId, courseName: courseName),
      ),
    );
  }

  /// 导航到文章详情
  static void toArticleDetail(BuildContext context, dynamic article) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => ArticleDetailPage(article: article),
      ),
    );
  }

  /// 导航到测验页面
  static void toQuiz(
    BuildContext context, {
    required int courseId,
    required String courseName,
    List<int>? questionIds,
    Map<int, Map<String, dynamic>>? courseInfoMap,
  }) {
    Navigator.push(
      context,
      CupertinoPageRoute(
        builder:
            (context) => quiz_page.QuizAttemptPage(
              courseId: courseId,
              courseName: courseName,
              questionIds: questionIds,
              courseInfoMap: courseInfoMap,
            ),
      ),
    );
  }

  /// 导航到收藏页面
  static void toFavorites(BuildContext context) {
    Navigator.pushNamed(context, favorites);
  }

  /// 导航到学习历史页面
  static void toStudyHistory(BuildContext context) {
    Navigator.pushNamed(context, studyHistory);
  }

  /// 导航到错题页面
  static void toWrongQuestions(BuildContext context) {
    Navigator.pushNamed(context, wrongQuestions);
  }

  /// 导航到排行榜页面
  static void toLeaderboard(BuildContext context) {
    Navigator.pushNamed(context, leaderboard);
  }

  /// 导航到管理员课程编辑页面
  static void toAdminCourseEdit(BuildContext context) {
    Navigator.pushNamed(context, adminCourseEdit);
  }

  /// 返回上一页面
  static void goBack(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  /// 返回到指定页面并清除其上的所有页面
  static void backToRoute(BuildContext context, String routeName) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      routeName,
      (Route<dynamic> route) => false,
    );
  }

  /// 弹出模态页面
  static Future<T?> showModal<T>(
    BuildContext context,
    Widget child, {
    bool barrierDismissible = true,
  }) {
    return showCupertinoModalPopup<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => child,
    );
  }

  /// 显示Cupertino风格的对话框
  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = '确定',
    String cancelText = '取消',
  }) {
    return showCupertinoDialog<bool>(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context, false),
                child: Text(cancelText),
              ),
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context, true),
                isDestructiveAction: true,
                child: Text(confirmText),
              ),
            ],
          ),
    );
  }
}
