import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wakeup_app/l10n/app_localizations.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';
import '../../widgets/animated_avatar_widget.dart';
import '../../constants/fonts.dart';
import 'dart:developer' as developer;

class KnowledgeCardPage extends StatefulWidget {
  const KnowledgeCardPage({super.key});

  @override
  State<KnowledgeCardPage> createState() => _KnowledgeCardPageState();
}

class _KnowledgeCardPageState extends State<KnowledgeCardPage> {

  @override
  void initState() {
    super.initState();
    developer.log('知识卡页面初始化');
  }

  @override
  Widget build(BuildContext context) {
    final titleText = AppLocalizations.of(context)!.navInformation;
    final isChinese = Localizations.localeOf(context).languageCode == 'zh';

    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 背景图片 - 用于液态玻璃效果
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: RadialGradient(
                  // 暗夜紫背景（中心略偏上方更亮）
                  center: Alignment(0.0, -0.2),
                  radius: 1.2,
                  colors: [Color(0xFF0F0520), Colors.black],
                  stops: [0.0, 1.0],
                ),
              ),
              // 添加一些装饰元素作为背景内容
              child: Stack(
                children: [
                  // 添加一些圆形装饰元素
                  Positioned(
                    top: 100,
                    left: 50,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [Color(0xFFBF5AF2), Color(0xFF7C4DFF)],
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 200,
                    right: 80,
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [Color(0xFF00E5FF), Color(0xFF0097A7)],
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 150,
                    left: 100,
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [Color(0xFFE91E63), Color(0xFFAD1457)],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 内容区域
          SafeArea(
            child: CustomScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                // 标题栏
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20, 16, 20, 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: (Platform.isIOS || Platform.isMacOS) && !isChinese
                              ? AppFonts.createBoldTextStack(
                                  titleText,
                                  fontSize: 36,
                                  letterSpacing: -0.5,
                                )
                              : Text(
                                  titleText,
                                  style: AppFonts.createTitleStyle(
                                    fontSize: 36,
                                    isLatinText: !isChinese,
                                  ),
                                ),
                        ),
                        const AnimatedAvatarWidget(size: 36),
                      ],
                    ),
                  ),
                ),

                // 空白页面内容
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 使用 liquid_glass_renderer 的液态玻璃效果
                        LiquidGlass(
                          blur: 25.0,
                          shape: LiquidRoundedSuperellipse(
                            borderRadius: Radius.circular(28),
                          ),
                          settings: const LiquidGlassSettings(
                            thickness: 10,
                            glassColor: Color(0x262D1B69), // 冷紫色调，约15%不透明度
                            lightIntensity: 1.2,
                          ),
                          child: _buildCardContent(),
                        ),
                        const SizedBox(height: 32),
                        Text(
                          '液态玻璃知识卡（使用 Liquid Glass Renderer）',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  // 卡片内容
  Widget _buildCardContent() {
    return SizedBox(
      width: 320,
      height: 380,
      child: _buildMacLikeCardContent(context),
    );
  }

  // 仿照截图的卡片内容（图标 + 文案 + 按钮）
  Widget _buildMacLikeCardContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // macOS 图标占位
          Container(
            width: 64,
            height: 64,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [Color(0xFFB388FF), Color(0xFF7C4DFF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: const Icon(Icons.tag_faces, color: Colors.white, size: 40),
          ),
          const SizedBox(height: 16),
          const Text(
            'Apple MacBook',
            style: TextStyle(
              color: Colors.white,
              fontSize: 22,
              fontWeight: FontWeight.w700,
              letterSpacing: 0.2,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '16 inch, November 2025',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 16),
          // Info 列表简化
          _infoRow('Chip', 'Apple M10 Max'),
          _infoRow('Memory', '256GB'),
          _infoRow('Startup disk', 'Macintosh HD'),
          _infoRow('Serial number', 'X0010XUYSZX'),
          _infoRow('macOS', 'Sequoia 20.0'),
          const SizedBox(height: 12),
          // 按钮
          Container(
            width: double.infinity,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: const LinearGradient(
                colors: [Color(0xFF7C4DFF), Color(0xFFBF5AF2)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Color(0xFFBF5AF2).withValues(alpha: 0.35),
                  blurRadius: 16,
                  spreadRadius: 1,
                ),
              ],
            ),
            alignment: Alignment.center,
            child: const Text(
              'More Info',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        children: [
          SizedBox(
            width: 110,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.left,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    developer.log('知识卡页面销毁');
  }
}