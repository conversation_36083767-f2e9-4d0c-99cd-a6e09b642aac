import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';

class AdminCourseEditPage extends StatefulWidget {
  const AdminCourseEditPage({super.key});

  @override
  State<AdminCourseEditPage> createState() => _AdminCourseEditPageState();
}

class _AdminCourseEditPageState extends State<AdminCourseEditPage> {
  late Future<List<Map<String, dynamic>>> _coursesFuture;
  final _formKey = GlobalKey<FormState>();
  final Map<int, TextEditingController> _englishNameControllers = {};

  @override
  void initState() {
    super.initState();
    _coursesFuture = _fetchCourses();
  }

  @override
  void dispose() {
    _englishNameControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  Future<List<Map<String, dynamic>>> _fetchCourses() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) {
      throw Exception('需要登录才能访问');
    }

    final response = await http.get(
      Uri.parse('${AuthService.baseUrl}/api/categories/level4/all'),
      headers: {
        'Authorization': 'Bearer ${userProvider.token}',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      final List<Map<String, dynamic>> courses = [];

      for (var item in data) {
        final Map<String, dynamic> course = Map<String, dynamic>.from(item);
        // 为每个课程创建一个文本控制器
        _englishNameControllers[course['id']] = TextEditingController(
          text: course['english_name'] ?? '',
        );
        courses.add(course);
      }

      return courses;
    } else {
      throw Exception('获取课程失败: ${response.statusCode}');
    }
  }

  Future<void> _updateCourseEnglishName(
    int courseId,
    String englishName,
  ) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    try {
      final response = await http.post(
        Uri.parse(
          '${AuthService.baseUrl}/api/categories/level4/update_english_name',
        ),
        headers: {
          'Authorization': 'Bearer ${userProvider.token}',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({'course_id': courseId, 'english_name': englishName}),
      );

      if (!mounted) return;
      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('更新成功')));
      } else {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('更新失败: ${response.statusCode}')));
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('更新出错: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('课程英文名称管理'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black, Color(0xFF1F1F1F)],
          ),
        ),
        child: FutureBuilder<List<Map<String, dynamic>>>(
          future: _coursesFuture,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(
                child: Text(
                  '错误: ${snapshot.error}',
                  style: const TextStyle(color: Colors.white),
                ),
              );
            }

            final courses = snapshot.data!;

            return Form(
              key: _formKey,
              child: ListView.builder(
                itemCount: courses.length,
                itemBuilder: (context, index) {
                  final course = courses[index];
                  final controller = _englishNameControllers[course['id']]!;

                  return Card(
                    margin: const EdgeInsets.all(8.0),
                    color: Colors.grey[900],
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${course['name']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: controller,
                            style: const TextStyle(color: Colors.white),
                            decoration: const InputDecoration(
                              labelText: '英文名称',
                              labelStyle: TextStyle(color: Colors.white70),
                              enabledBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.white30),
                              ),
                              focusedBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.white),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              ElevatedButton(
                                onPressed: () {
                                  _updateCourseEnglishName(
                                    course['id'],
                                    controller.text,
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('保存'),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}
