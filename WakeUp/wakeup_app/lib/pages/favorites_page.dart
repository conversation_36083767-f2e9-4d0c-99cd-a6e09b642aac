import 'dart:io' show Platform;
import 'dart:ui';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/fonts.dart';
import '../widgets/back_button.dart';
import 'quiz/quiz_attempt_page.dart' as quiz_page;
import '../core/utils/logger.dart';

class FavoritesPage extends StatefulWidget {
  final int? courseId;

  const FavoritesPage({super.key, this.courseId});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage>
    with TickerProviderStateMixin {
  List<Map<String, dynamic>> _favorites = [];
  String _selectedTab = "current";
  TabController? _tabController;

  bool _isCurrentCourseLoading = true;
  List<Map<String, dynamic>> _currentCourseFavorites = [];
  bool _isAllCoursesLoading = true;
  List<Map<String, dynamic>> _allCoursesFavorites = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController!.addListener(() {
      final newIndex = _tabController!.index;
      if (newIndex == 0 && _selectedTab != "current") {
        setState(() {
          _selectedTab = "current";
        });
      } else if (newIndex == 1 && _selectedTab != "all") {
        setState(() {
          _selectedTab = "all";
        });
      }
    });
    _loadAndPrepareAllData();
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  Future<void> _loadAndPrepareAllData() async {
    setState(() {
      _isCurrentCourseLoading = true;
      _isAllCoursesLoading = true;
    });

    try {
      // 从SharedPreferences获取收藏数据
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorites') ?? '[]';
      AppLogger.info('获取到收藏列表JSON: $favoritesJson');

      final List decodedData = jsonDecode(favoritesJson);
      AppLogger.info('收藏列表中有${decodedData.length}个项目');

      // 将JSON数据转换为可用的Map列表
      _favorites =
          decodedData
              .map<Map<String, dynamic>>(
                (item) => item is Map ? Map<String, dynamic>.from(item) : {},
              )
              .toList();

      // 移除可能出现的空Map
      _favorites.removeWhere((item) => item.isEmpty);

      AppLogger.info('处理后的收藏列表包含${_favorites.length}个项目');
    } catch (e) {
      AppLogger.error('加载收藏数据失败', error: e);
      // 出错时使用空列表
      _favorites = [];
    }

    _prepareTabData();
    if (mounted) {
      setState(() {});
    }
  }

  void _prepareTabData() {
    if (widget.courseId != null) {
      _currentCourseFavorites =
          _favorites.where((q) => q['courseId'] == widget.courseId).toList();
    } else {
      _currentCourseFavorites = [];
    }
    _isCurrentCourseLoading = false;

    _allCoursesFavorites = List.from(_favorites);
    _isAllCoursesLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/traditional_quiz_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPageForTab("current"),
                    _buildPageForTab("all"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 34,
              height: 34,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.15),
                shape: BoxShape.circle,
              ),
              child: BackButtonWidget(
                text: "",
                color: Colors.white,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              "收藏库",
              style:
                  Platform.isIOS &&
                          Localizations.localeOf(context).languageCode != 'zh'
                      ? TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      )
                      : AppFonts.createTitleStyle(
                        fontSize: 18,
                        isLatinText:
                            Localizations.localeOf(context).languageCode !=
                            'zh',
                      ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          // 平衡布局的空白区域，保持与返回按钮宽度大致对称
          SizedBox(width: 34 + 16), // 34 for button, 16 for SizedBox
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: [Tab(text: "当前课程"), Tab(text: "全部课程")],
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
      indicatorColor: Colors.white,
      indicatorWeight: 3.0,
      indicatorSize: TabBarIndicatorSize.label,
      labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: 15,
      ),
    );
  }

  Widget _buildPageForTab(String tabType) {
    bool isLoading;
    List<Map<String, dynamic>> specificData;

    if (tabType == "current") {
      isLoading = _isCurrentCourseLoading;
      specificData = _currentCourseFavorites;
    } else {
      // "all"
      isLoading = _isAllCoursesLoading;
      specificData = _allCoursesFavorites;
    }

    if (isLoading) {
      return Center(child: CupertinoActivityIndicator());
    }
    if (specificData.isEmpty) {
      return _buildEmptyState();
    }
    return _buildFavoritesList(specificData, tabType);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            CupertinoIcons.bookmark,
            size: 60,
            color: Colors.white.withValues(alpha: 0.6),
          ),
          SizedBox(height: 16),
          Text(
            _selectedTab == "current" && widget.courseId == null
                ? "未指定当前课程"
                : "暂无收藏题目",
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            "在答题页面点击收藏按钮可添加题目",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList(
    List<Map<String, dynamic>> currentFavoritesData,
    String tabType,
  ) {
    // 提取当前列表中所有题目的ID
    final List<int> questionIds =
        currentFavoritesData
            .map<int>((item) {
              // 确保正确处理id字段，可能是int或string
              var id = item['id'];
              if (id is int) {
                return id;
              } else if (id is String) {
                return int.parse(id);
              }
              // 如果无法解析，返回默认值-1
              AppLogger.warning('无法解析题目ID: $id');
              return -1;
            })
            .where((id) => id != -1) // 过滤掉无效的ID
            .toList();

    AppLogger.info(
      '${tabType == "current" ? "当前课程" : "全部课程"}收藏库题目ID列表: $questionIds',
    );

    // 构建题目ID到课程信息的映射
    final Map<int, Map<String, dynamic>> courseInfoMap = {};
    for (var item in currentFavoritesData) {
      try {
        final id =
            item['id'] is int ? item['id'] : int.parse(item['id'].toString());
        courseInfoMap[id] = {
          'courseId':
              item['courseId'] is int
                  ? item['courseId']
                  : int.parse(item['courseId'].toString()),
          'courseName': item['courseName'],
        };
      } catch (e) {
        AppLogger.error('构建课程信息映射出错', error: e);
      }
    }

    AppLogger.info('构建了包含${courseInfoMap.length}条记录的课程信息映射');

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: currentFavoritesData.length,
      itemBuilder: (context, index) {
        final item = currentFavoritesData[index];
        return _buildFavoriteCard(item, questionIds, tabType, courseInfoMap);
      },
    );
  }

  Widget _buildFavoriteCard(
    Map<String, dynamic> item,
    List<int> allQuestionIds,
    String tabType,
    Map<int, Map<String, dynamic>> courseInfoMap,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        item['courseName'],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Spacer(),
                    Text(
                      item['date'],
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                Text(
                  item['content'],
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  children: [
                    _buildActionButton(
                      icon: CupertinoIcons.arrow_right_circle,
                      text: "开始答题",
                      onTap: () {
                        // 导航到答题页面，传递当前标签下的所有题目ID和课程信息映射
                        AppLogger.info(
                          '从${tabType == "current" ? "当前课程" : "全部课程"}收藏库跳转到答题页面',
                        );
                        Navigator.of(context).push(
                          CupertinoPageRoute(
                            builder:
                                (context) => quiz_page.QuizAttemptPage(
                                  courseId: item['courseId'],
                                  courseName: item['courseName'],
                                  questionIds: allQuestionIds,
                                  courseInfoMap: courseInfoMap,
                                ),
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 12),
                    _buildActionButton(
                      icon: CupertinoIcons.trash,
                      text: "移出收藏",
                      onTap: () {
                        _showRemoveConfirmation(item);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(icon, size: 16, color: Colors.white),
            SizedBox(width: 6),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示删除确认对话框
  Future<void> _showRemoveConfirmation(Map<String, dynamic> item) async {
    bool? confirmed = await showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final bottomPadding = MediaQuery.of(context).padding.bottom;

        // 辅助方法构建操作项
        Widget buildActionItem(
          String text,
          VoidCallback onPressed, {
          bool isDestructive = false,
          FontWeight fontWeight = FontWeight.normal,
        }) {
          return GestureDetector(
            onTap: onPressed,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              alignment: Alignment.center,
              child: Text(
                text,
                style: TextStyle(
                  color: const Color(0xFFE0464C), // 统一使用自定义红色
                  fontSize: 17.0, // 统一字体大小
                  fontWeight: fontWeight,
                ),
              ),
            ),
          );
        }

        return Padding(
          // 外层Padding，用于屏幕边缘和底部安全区域
          padding: EdgeInsets.only(
            left: 8.0,
            right: 8.0,
            bottom: bottomPadding > 0 ? bottomPadding : 8.0,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end, // 整体底部对齐
            children: [
              // ----- 标题和主要操作区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15.0,
                    sigmaY: 15.0,
                  ), // 更强的模糊效果
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withValues(alpha: 0.92), // 更准确的背景色
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                            horizontal: 20.0,
                          ),
                          child: Text(
                            "确定要从收藏库删除这个题目吗？",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Color(0xFFE5E5E7),
                              fontSize: 13.0,
                              fontWeight: FontWeight.bold, // 标题加粗
                            ),
                          ),
                        ),
                        // 分隔线
                        Divider(
                          height: 0.5,
                          thickness: 0.5,
                          color: Colors.grey[700]?.withValues(alpha: 0.5),
                        ),
                        // "从收藏库删除" 按钮
                        buildActionItem(
                          "从收藏库删除",
                          () => Navigator.of(context).pop(true),
                          fontWeight: FontWeight.bold, // "从收藏库删除"按钮加粗
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8.0), // 主要操作区和取消按钮之间的间距
              // ----- 取消按钮区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15.0,
                    sigmaY: 15.0,
                  ), // 更强的模糊效果
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withValues(alpha: 0.92), // 更准确的背景色
                    ),
                    child: buildActionItem(
                      "取消",
                      () => Navigator.of(context).pop(false),
                      fontWeight: FontWeight.bold, // "取消"按钮加粗
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    if (confirmed == true) {
      await _removeFromFavorites(item);
    }
  }

  // 从收藏中移除项目
  Future<void> _removeFromFavorites(Map<String, dynamic> item) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorites') ?? '[]';
      AppLogger.info('获取到原收藏列表: $favoritesJson');

      final List favorites = jsonDecode(favoritesJson);
      AppLogger.info('原收藏列表包含${favorites.length}个项目');

      // 移除匹配的收藏项
      final updatedFavorites =
          favorites.where((favItem) {
            final bool shouldKeep =
                !(favItem is Map &&
                    favItem['id'] == item['id'] &&
                    favItem['courseId'] == item['courseId']);

            if (!shouldKeep) {
              AppLogger.info('找到要移除的收藏项: ${favItem.toString()}');
            }
            return shouldKeep;
          }).toList();

      AppLogger.info('更新后收藏列表包含${updatedFavorites.length}个项目');

      // 保存更新后的收藏列表
      final newFavoritesJson = jsonEncode(updatedFavorites);
      AppLogger.info('新收藏列表JSON: $newFavoritesJson');
      await prefs.setString('favorites', newFavoritesJson);
      AppLogger.success('已保存新收藏列表到SharedPreferences');

      // 重新加载数据
      _loadAndPrepareAllData();
    } catch (e) {
      AppLogger.error('移除收藏失败', error: e);
      if (mounted) {
        _showNotification('从收藏库删除失败，请重试');
      }
    }
  }

  // 显示通知提示
  void _showNotification(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.red.shade800,
        duration: Duration(seconds: 2),
      ),
    );
  }
}
