import 'package:flutter/material.dart';
import '../../../constants/fonts.dart';
import '../controllers/register_controller.dart';

/// 简化的注册步骤指示器组件 - 只显示当前步骤标题
class RegisterStepIndicator extends StatelessWidget {
  final RegisterStep currentStep;

  const RegisterStepIndicator({
    super.key,
    required this.currentStep,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 32, 20, 20),
      child: Center(
        child: Text(
          _getStepTitle(currentStep),
          style: AppFonts.createMixedStyle(
            fontSize: 24,
            fontWeight: FontWeight.w700,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }

  String _getStepTitle(RegisterStep step) {
    switch (step) {
      case RegisterStep.phone:
        return '输入手机号';
      case RegisterStep.phoneVerification:
        return '验证手机号';
      case RegisterStep.email:
        return '输入邮箱';
      case RegisterStep.emailVerification:
        return '验证邮箱';
      case RegisterStep.username:
        return '设置用户名';
      case RegisterStep.password:
        return '设置密码';
      case RegisterStep.confirmPassword:
        return '确认密码';
      case RegisterStep.nickname:
        return '设置昵称';
      case RegisterStep.gender:
        return '选择性别';
      case RegisterStep.region:
        return '选择地区';
      case RegisterStep.courses:
        return '选择课程';
    }
  }
}