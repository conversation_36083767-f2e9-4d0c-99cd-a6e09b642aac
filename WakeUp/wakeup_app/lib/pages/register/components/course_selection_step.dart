import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/fonts.dart';

/// 课程选择步骤组件 - 深色主题版本
class CourseSelectionStep extends StatefulWidget {
  final List<dynamic> selectedCourses;
  final Function(List<dynamic>) onCoursesChanged;
  final bool isLoading;

  const CourseSelectionStep({
    super.key,
    required this.selectedCourses,
    required this.onCoursesChanged,
    this.isLoading = false,
  });

  @override
  State<CourseSelectionStep> createState() => _CourseSelectionStepState();
}

class _CourseSelectionStepState extends State<CourseSelectionStep> {
  late TextEditingController _regionController;
  String? _selectedRegion;
  List<Map<String, dynamic>> _availableCourses = [];

  // 课程数据（从API获取）
  final Map<String, List<Map<String, dynamic>>> _coursesByRegion = {
    '北京（测试）': [
      {'id': 1, 'name': '高等数学（测试）', 'credit': 4, 'teacher': '张教授（测试）'},
      {'id': 2, 'name': '线性代数（测试）', 'credit': 3, 'teacher': '李教授（测试）'},
      {'id': 3, 'name': '概率论（测试）', 'credit': 3, 'teacher': '王教授（测试）'},
      {'id': 4, 'name': '数据结构（测试）', 'credit': 4, 'teacher': '赵教授（测试）'},
    ],
    '上海（测试）': [
      {'id': 5, 'name': '高等数学（测试）', 'credit': 4, 'teacher': '陈教授（测试）'},
      {'id': 6, 'name': '线性代数（测试）', 'credit': 3, 'teacher': '刘教授（测试）'},
      {'id': 7, 'name': '计算机网络（测试）', 'credit': 3, 'teacher': '杨教授（测试）'},
      {'id': 8, 'name': '操作系统（测试）', 'credit': 4, 'teacher': '周教授（测试）'},
    ],
    '广州（测试）': [
      {'id': 9, 'name': '高等数学（测试）', 'credit': 4, 'teacher': '吴教授（测试）'},
      {'id': 10, 'name': '英语（测试）', 'credit': 2, 'teacher': '郑教授（测试）'},
      {'id': 11, 'name': '物理（测试）', 'credit': 3, 'teacher': '孙教授（测试）'},
      {'id': 12, 'name': '化学（测试）', 'credit': 3, 'teacher': '马教授（测试）'},
    ],
  };

  @override
  void initState() {
    super.initState();
    _regionController = TextEditingController();
  }

  @override
  void dispose() {
    _regionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明文字 - 深色主题适配
          Text(
            '选择您的地区和感兴趣的课程',
            style: AppFonts.createMixedStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),

          const SizedBox(height: 24),

          _buildRegionSelector(),

          const SizedBox(height: 24),

          if (_availableCourses.isNotEmpty) ...[
            _buildCourseList(),
            const SizedBox(height: 24),
          ],

          if (widget.selectedCourses.isNotEmpty) _buildSelectedCourses(),
        ],
      ),
    );
  }

  Widget _buildRegionSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择地区',
          style: AppFonts.createMixedStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 12),

        Container(
          height: 56,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
          ),
          child: CupertinoButton(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            onPressed: () {
              HapticFeedback.selectionClick();
              _showRegionPicker();
            },
            child: Row(
              children: [
                Icon(
                  CupertinoIcons.location,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _selectedRegion ?? '请选择地区',
                    style: AppFonts.createMixedStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.5,
                      color:
                          _selectedRegion != null
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.5),
                    ),
                  ),
                ),
                Icon(
                  CupertinoIcons.chevron_down,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCourseList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '可选课程',
          style: AppFonts.createMixedStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 12),

        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _availableCourses.length,
            separatorBuilder:
                (context, index) =>
                    Container(height: 1, color: Colors.white.withValues(alpha: 0.1)),
            itemBuilder: (context, index) {
              final course = _availableCourses[index];
              final isSelected = widget.selectedCourses.any(
                (selected) => selected['id'] == course['id'],
              );

              return CupertinoButton(
                padding: const EdgeInsets.all(16),
                onPressed: () {
                  HapticFeedback.selectionClick();
                  _toggleCourse(course);
                },
                child: Row(
                  children: [
                    Icon(
                      isSelected
                          ? CupertinoIcons.check_mark_circled_solid
                          : CupertinoIcons.circle,
                      color:
                          isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.4),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            course['name'],
                            style: AppFonts.createMixedStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${course['credit']}学分 • ${course['teacher']}',
                            style: AppFonts.createMixedStyle(
                              fontSize: 14,
                              color: Colors.white.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedCourses() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '已选课程 (${widget.selectedCourses.length})',
          style: AppFonts.createMixedStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 12),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              widget.selectedCourses.map((course) {
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        course['name'],
                        style: AppFonts.createMixedStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 4),
                      GestureDetector(
                        onTap: () {
                          HapticFeedback.selectionClick();
                          _removeCourse(course);
                        },
                        child: Icon(
                          CupertinoIcons.xmark,
                          size: 14,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  void _showRegionPicker() {
    showCupertinoModalPopup(
      context: context,
      builder:
          (context) => Container(
            height: 250,
            decoration: const BoxDecoration(
              color: Color(0xFF1a1a1a),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.white.withValues(alpha: 0.1),
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CupertinoButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          '取消',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.7),
                            fontSize: 16,
                          ),
                        ),
                      ),
                      Text(
                        '选择地区',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      CupertinoButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text(
                          '确定',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: CupertinoPicker(
                    itemExtent: 32,
                    onSelectedItemChanged: (index) {
                      final regions = _coursesByRegion.keys.toList();
                      _selectedRegion = regions[index];
                      _availableCourses =
                          _coursesByRegion[_selectedRegion] ?? [];
                      setState(() {});
                    },
                    children:
                        _coursesByRegion.keys.map((region) {
                          return Center(
                            child: Text(
                              region,
                              style: AppFonts.createMixedStyle(
                                fontSize: 16,
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _toggleCourse(Map<String, dynamic> course) {
    final selectedCourses = List<dynamic>.from(widget.selectedCourses);
    final existingIndex = selectedCourses.indexWhere(
      (selected) => selected['id'] == course['id'],
    );

    if (existingIndex >= 0) {
      selectedCourses.removeAt(existingIndex);
    } else {
      selectedCourses.add(course);
    }

    widget.onCoursesChanged(selectedCourses);
  }

  void _removeCourse(Map<String, dynamic> course) {
    final selectedCourses = List<dynamic>.from(widget.selectedCourses);
    selectedCourses.removeWhere((selected) => selected['id'] == course['id']);
    widget.onCoursesChanged(selectedCourses);
  }
}