import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/fonts.dart';

/// 邮箱验证步骤组件
class EmailVerificationStep extends StatefulWidget {
  final String? initialEmail;
  final Function(String) onEmailChanged;
  final Function(String)? onCodeChanged;
  final VoidCallback? onSendCode;
  final bool isLoading;

  const EmailVerificationStep({
    super.key,
    this.initialEmail,
    required this.onEmailChanged,
    this.onCodeChanged,
    this.onSendCode,
    this.isLoading = false,
  });

  @override
  State<EmailVerificationStep> createState() => _EmailVerificationStepState();
}

class _EmailVerificationStepState extends State<EmailVerificationStep> {
  late TextEditingController _emailController;
  late TextEditingController _codeController;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController(text: widget.initialEmail);
    _codeController = TextEditingController();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明文字 - 深色主题适配
          Text(
            '我们将向您的邮箱发送验证码',
            style: AppFonts.createMixedStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
          
          const SizedBox(height: 24),
          
          _buildEmailInput(),
          
          const SizedBox(height: 16),
          
          _buildCodeInput(),
          
          const SizedBox(height: 24),
          
          // 发送验证码按钮 - 深色主题适配
          SizedBox(
            width: double.infinity,
            child: CupertinoButton(
              padding: const EdgeInsets.symmetric(vertical: 16),
              onPressed: widget.isLoading ? null : widget.onSendCode,
              color: widget.isLoading 
                  ? Colors.white.withValues(alpha: 0.3)
                  : Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(8),
              child: widget.isLoading
                  ? const CupertinoActivityIndicator(color: Colors.black)
                  : Text(
                      '发送验证码',
                      style: AppFonts.createMixedStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailInput() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: CupertinoTextField(
        controller: _emailController,
        placeholder: '请输入邮箱地址',
        keyboardType: TextInputType.emailAddress,
        decoration: const BoxDecoration(),
        style: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        placeholderStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.5),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        onTap: () => HapticFeedback.selectionClick(),
        prefix: Container(
          padding: const EdgeInsets.only(left: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                CupertinoIcons.mail,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
        onChanged: widget.onEmailChanged,
      ),
    );
  }

  Widget _buildCodeInput() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: CupertinoTextField(
        controller: _codeController,
        placeholder: '请输入6位验证码',
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(6),
        ],
        decoration: const BoxDecoration(),
        style: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        placeholderStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.5),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        onTap: () => HapticFeedback.selectionClick(),
        prefix: Container(
          padding: const EdgeInsets.only(left: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                CupertinoIcons.textformat_123,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
        onChanged: widget.onCodeChanged,
      ),
    );
  }
}