import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../constants/fonts.dart';

/// 手机验证步骤组件
class PhoneVerificationStep extends StatefulWidget {
  final String? initialPhone;
  final Function(String) onPhoneChanged;
  final VoidCallback? onSendCode;
  final bool isLoading;

  const PhoneVerificationStep({
    super.key,
    this.initialPhone,
    required this.onPhoneChanged,
    this.onSendCode,
    this.isLoading = false,
  });

  @override
  State<PhoneVerificationStep> createState() => _PhoneVerificationStepState();
}

class _PhoneVerificationStepState extends State<PhoneVerificationStep> {
  late TextEditingController _phoneController;
  late TextEditingController _codeController;

  @override
  void initState() {
    super.initState();
    _phoneController = TextEditingController(text: widget.initialPhone);
    _codeController = TextEditingController();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 说明文字 - 深色主题适配
          Text(
            '我们将向您的手机发送验证码',
            style: AppFonts.createMixedStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.7),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 手机号输入
          _buildPhoneInput(),
          
          const SizedBox(height: 16),
          
          // 验证码输入
          _buildCodeInput(),
          
          const SizedBox(height: 24),
          
          // 发送验证码按钮 - 深色主题适配
          SizedBox(
            width: double.infinity,
            child: CupertinoButton(
              padding: const EdgeInsets.symmetric(vertical: 16),
              onPressed: widget.isLoading ? null : widget.onSendCode,
              color: widget.isLoading 
                  ? Colors.white.withValues(alpha: 0.3)
                  : Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(8),
              child: widget.isLoading
                  ? const CupertinoActivityIndicator(color: Colors.black)
                  : Text(
                      '发送验证码',
                      style: AppFonts.createMixedStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneInput() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: CupertinoTextField(
        controller: _phoneController,
        placeholder: '请输入手机号',
        keyboardType: TextInputType.phone,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(11),
        ],
        decoration: const BoxDecoration(),
        style: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        placeholderStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.5),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        onTap: () => HapticFeedback.selectionClick(),
        prefix: Container(
          padding: const EdgeInsets.only(left: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                CupertinoIcons.phone,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '+86',
                style: AppFonts.createMixedStyle(
                  fontSize: 16,
                  color: Colors.white.withValues(alpha: 0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 1,
                height: 20,
                color: Colors.white.withValues(alpha: 0.2),
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
        onChanged: widget.onPhoneChanged,
      ),
    );
  }

  Widget _buildCodeInput() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: CupertinoTextField(
        controller: _codeController,
        placeholder: '请输入6位验证码',
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(6),
        ],
        decoration: const BoxDecoration(),
        style: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
        placeholderStyle: AppFonts.createMixedStyle(
          fontSize: 16,
          color: Colors.white.withValues(alpha: 0.5),
          fontWeight: FontWeight.w400,
          letterSpacing: 0.5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        onTap: () => HapticFeedback.selectionClick(),
        prefix: Container(
          padding: const EdgeInsets.only(left: 4),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                CupertinoIcons.textformat_123,
                color: Colors.white.withValues(alpha: 0.7),
                size: 20,
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
        onChanged: (value) {
          // 可以添加验证码变化的回调
        },
      ),
    );
  }
}