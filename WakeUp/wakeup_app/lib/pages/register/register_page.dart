// 注册页面入口点（重构版说明见 refactored_register_page.dart）
export 'refactored_register_page.dart' show RefactoredRegisterPage;

// 为了向后兼容，保留原有的RegisterPage类名
// 实际实现重定向到重构后的版本
import 'package:flutter/cupertino.dart';
import 'refactored_register_page.dart';
import '../auth/login_page.dart' show AccountType;

class RegisterPage extends StatelessWidget {
  final String? prefilledAccount;
  final AccountType? prefilledAccountType;

  const RegisterPage({
    super.key,
    this.prefilledAccount,
    this.prefilledAccountType,
  });

  @override
  Widget build(BuildContext context) {
    // 重定向到重构后的注册页面
    return const RefactoredRegisterPage();
  }
}