import 'dart:io' show Platform;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:wakeup_app/core/utils/logger.dart';
import '../constants/fonts.dart';
import '../widgets/back_button.dart';
import 'quiz/quiz_attempt_page.dart' as quiz_page;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class StudyHistoryPage extends StatefulWidget {
  final int? courseId;

  const StudyHistoryPage({super.key, this.courseId});

  @override
  State<StudyHistoryPage> createState() => _StudyHistoryPageState();
}

class _StudyHistoryPageState extends State<StudyHistoryPage>
    with TickerProviderStateMixin {
  List<Map<String, dynamic>> _historyQuestions = [];
  String _selectedTab = "current";
  TabController? _tabController;

  bool _isCurrentCourseLoading = true;
  List<Map<String, dynamic>> _currentCourseHistoryQuestions = [];
  bool _isAllCoursesLoading = true;
  List<Map<String, dynamic>> _allCoursesHistoryQuestions = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController!.addListener(() {
      final newIndex = _tabController!.index;
      if (newIndex == 0 && _selectedTab != "current") {
        setState(() {
          _selectedTab = "current";
        });
      } else if (newIndex == 1 && _selectedTab != "all") {
        setState(() {
          _selectedTab = "all";
        });
      }
    });

    // 在initState中添加标志位，防止didChangeDependencies中重复加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAndPrepareAllData();
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面依赖变化时，不再自动加载数据，避免重复
    // 数据加载由initState和didUpdateWidget处理
  }

  @override
  void didUpdateWidget(StudyHistoryPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果课程ID发生变化，重新加载数据
    if (oldWidget.courseId != widget.courseId) {
      AppLogger.info('课程ID发生变化: ${oldWidget.courseId} -> ${widget.courseId}');
      _loadAndPrepareAllData();
    }
  }

  Future<void> _loadAndPrepareAllData() async {
    setState(() {
      _isCurrentCourseLoading = true;
      _isAllCoursesLoading = true;
    });

    try {
      // 从SharedPreferences获取学习历史数据
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('study_history') ?? '[]';
      AppLogger.info('获取到学习历史JSON: $historyJson');

      final List decodedData = jsonDecode(historyJson);
      AppLogger.info('学习历史中有${decodedData.length}个项目');

      // 将JSON数据转换为可用的Map列表
      _historyQuestions =
          decodedData
              .map<Map<String, dynamic>>(
                (item) => item is Map ? Map<String, dynamic>.from(item) : {},
              )
              .toList();

      // 移除可能出现的空Map
      _historyQuestions.removeWhere((item) => item.isEmpty);

      AppLogger.info('处理后的学习历史包含${_historyQuestions.length}个项目');
    } catch (e) {
      AppLogger.error('加载学习历史数据失败', error: e);
      // 出错时使用空列表
      _historyQuestions = [];
    }

    _prepareTabData();
    if (mounted) {
      setState(() {});
    }
  }

  void _prepareTabData() {
    // Prepare data for "current" tab
    if (widget.courseId != null) {
      _currentCourseHistoryQuestions =
          _historyQuestions.where((q) {
            // 确保类型一致进行比较，从SharedPreferences读取的数据可能会将数字转为字符串
            var questionCourseId = q['courseId'];
            // 转换为相同类型进行比较
            if (questionCourseId is String) {
              return int.parse(questionCourseId) == widget.courseId;
            } else if (questionCourseId is int) {
              return questionCourseId == widget.courseId;
            }
            return false;
          }).toList();

      // 打印调试信息
      AppLogger.info('当前课程ID: ${widget.courseId}');
      AppLogger.info('当前课程筛选结果：${_currentCourseHistoryQuestions.length}条记录');
    } else {
      _currentCourseHistoryQuestions = [];
    }
    _isCurrentCourseLoading = false;

    // Prepare data for "all" tab
    _allCoursesHistoryQuestions = List.from(_historyQuestions);
    _isAllCoursesLoading = false;

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    // 添加打印语句，帮助调试
    AppLogger.info('StudyHistoryPage build: courseId=${widget.courseId}, 当前课程数据数量=${_currentCourseHistoryQuestions.length}, 全部课程数据数量=${_allCoursesHistoryQuestions.length}');

    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/traditional_quiz_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildAppBar(),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildPageForTab("current"),
                    _buildPageForTab("all"),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          BackButtonWidget(
            text: "",
            color: Colors.white,
            onPressed: () => Navigator.of(context).pop(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              "我的学习历史",
              style:
                  Platform.isIOS &&
                          Localizations.localeOf(context).languageCode != 'zh'
                      ? TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      )
                      : AppFonts.createTitleStyle(
                        fontSize: 18,
                        isLatinText:
                            Localizations.localeOf(context).languageCode !=
                            'zh',
                      ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          // 平衡布局的空白区域，保持与返回按钮宽度大致对称
          SizedBox(width: 34 + 16), // 34 for button, 16 for SizedBox
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      tabs: [Tab(text: "当前课程"), Tab(text: "全部课程")],
      labelColor: Colors.white,
      unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
      indicatorColor: Colors.white,
      indicatorWeight: 3.0,
      indicatorSize: TabBarIndicatorSize.label,
      labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
      unselectedLabelStyle: TextStyle(
        fontWeight: FontWeight.normal,
        fontSize: 15,
      ),
    );
  }

  Widget _buildPageForTab(String tabType) {
    bool isLoading;
    List<Map<String, dynamic>> specificData;

    // 添加调试输出，帮助诊断问题
    AppLogger.info('构建选项卡: tabType=$tabType, courseId=${widget.courseId}');

    if (tabType == "current") {
      isLoading = _isCurrentCourseLoading;
      specificData = _currentCourseHistoryQuestions;
      AppLogger.info('当前课程标签: isLoading=$isLoading, 数据项数量=${specificData.length}');
    } else {
      // "all"
      isLoading = _isAllCoursesLoading;
      specificData = _allCoursesHistoryQuestions;
      AppLogger.info('全部课程标签: isLoading=$isLoading, 数据项数量=${specificData.length}');
    }

    if (isLoading) {
      return Center(child: CupertinoActivityIndicator());
    }
    if (specificData.isEmpty) {
      // _buildEmptyState uses _selectedTab, which is synced by the TabController listener.
      return _buildEmptyState();
    }
    // 根据当前选中的标签视图传递参数
    return _buildHistoryList(specificData, tabType);
  }

  Widget _buildEmptyState() {
    // 添加调试输出
    AppLogger.info('构建空状态视图: selectedTab=$_selectedTab, courseId=${widget.courseId}');

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            CupertinoIcons.clock,
            size: 60,
            color: Colors.white.withValues(alpha: 0.6),
          ),
          SizedBox(height: 16),
          Text(
            _selectedTab == "current" && widget.courseId == null
                ? "未指定当前课程"
                : "暂无学习记录",
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            "完成题目后将自动记录到学习历史中",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 6),
          Text(
            "只保留最近7天的学习记录",
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList(
    List<Map<String, dynamic>> currentHistoryData,
    String tabType,
  ) {
    // 提取当前列表中所有题目的ID
    final List<int> questionIds =
        currentHistoryData
            .map<int>((item) {
              // 确保正确处理id字段，可能是int或string
              var id = item['id'];
              if (id is int) {
                return id;
              } else if (id is String) {
                return int.parse(id);
              }
              // 如果无法解析，返回默认值-1
              AppLogger.warning('警告: 无法解析题目ID: $id');
              return -1;
            })
            .where((id) => id != -1) // 过滤掉无效的ID
            .toList();

    AppLogger.info('${tabType == "current" ? "当前课程" : "全部课程"}题目ID列表: $questionIds');

    // 构建题目ID到课程信息的映射
    final Map<int, Map<String, dynamic>> courseInfoMap = {};
    for (var item in currentHistoryData) {
      try {
        final id =
            item['id'] is int ? item['id'] : int.parse(item['id'].toString());
        courseInfoMap[id] = {
          'courseId':
              item['courseId'] is int
                  ? item['courseId']
                  : int.parse(item['courseId'].toString()),
          'courseName': item['courseName'],
        };
      } catch (e) {
        AppLogger.error('构建课程信息映射出错', error: e);
      }
    }

    AppLogger.info('构建了包含${courseInfoMap.length}条记录的课程信息映射');

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: currentHistoryData.length,
      itemBuilder: (context, index) {
        final item = currentHistoryData[index];
        return _buildHistoryCard(item, questionIds, tabType, courseInfoMap);
      },
    );
  }

  Widget _buildHistoryCard(
    Map<String, dynamic> item,
    List<int> allQuestionIds,
    String tabType,
    Map<int, Map<String, dynamic>> courseInfoMap,
  ) {
    // 确定题目回答状态
    final bool isCorrect = item['isCorrect'] == true;

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        item['courseName'],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(width: 8),
                    // 显示题目回答状态
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isCorrect
                                ? Colors.green.withValues(alpha: 0.2)
                                : Colors.red.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            isCorrect
                                ? CupertinoIcons.check_mark
                                : CupertinoIcons.xmark,
                            size: 12,
                            color:
                                isCorrect ? Colors.green[200] : Colors.red[200],
                          ),
                          SizedBox(width: 4),
                          Text(
                            isCorrect ? "回答正确" : "回答错误",
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  isCorrect
                                      ? Colors.green[200]
                                      : Colors.red[200],
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    Text(
                      item['date'],
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),
                Text(
                  item['content'],
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  children: [
                    _buildActionButton(
                      icon: CupertinoIcons.arrow_right_circle,
                      text: "重新练习",
                      onTap: () {
                        // 导航到答题页面，传递当前标签下的所有题目ID和课程信息映射
                        AppLogger.info('从${tabType == "current" ? "当前课程" : "全部课程"}标签跳转到答题页面');
                        Navigator.of(context).push(
                          CupertinoPageRoute(
                            builder:
                                (context) => quiz_page.QuizAttemptPage(
                                  courseId: item['courseId'],
                                  courseName: item['courseName'],
                                  questionIds: allQuestionIds,
                                  courseInfoMap: courseInfoMap,
                                ),
                          ),
                        );
                      },
                    ),
                    SizedBox(width: 12),
                    _buildActionButton(
                      icon: CupertinoIcons.star,
                      text: "添加到收藏库",
                      onTap: () {
                        // 添加到收藏库
                        _addToFavorites(item);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
        ),
        child: Row(
          children: [
            Icon(icon, size: 16, color: Colors.white),
            SizedBox(width: 6),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 添加收藏的方法
  Future<void> _addToFavorites(Map<String, dynamic> item) async {
    try {
      AppLogger.info('开始添加收藏: 题目ID=${item['id']}, 课程ID=${item['courseId']}');

      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorites') ?? '[]';
      AppLogger.info('获取到原收藏列表: $favoritesJson');

      final favorites = jsonDecode(favoritesJson) as List;
      AppLogger.info('原收藏列表包含${favorites.length}个项目');

      // 检查题目是否已经在收藏库中
      bool isAlreadyInFavorites = favorites.any(
        (favItem) =>
            favItem is Map &&
            favItem['id'] == item['id'] &&
            favItem['courseId'] == item['courseId'],
      );

      if (isAlreadyInFavorites) {
        AppLogger.info('题目已在收藏库中');
        if (!mounted) return;
        // 显示提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('该题目已在收藏库中'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.orange.shade800,
            duration: const Duration(seconds: 2),
          ),
        );
        return;
      }

      // 创建新的收藏项
      final newFavorite = {
        'id': item['id'],
        'content': item['content'],
        'courseId': item['courseId'],
        'courseName': item['courseName'],
        'date': DateTime.now().toString().substring(0, 10),
      };

      AppLogger.info('创建新收藏项: $newFavorite');

      // 添加到收藏列表
      favorites.add(newFavorite);
      AppLogger.info('更新后收藏列表包含${favorites.length}个项目');

      // 保存更新后的收藏列表
      final newFavoritesJson = jsonEncode(favorites);
      AppLogger.info('新收藏列表JSON: $newFavoritesJson');
      await prefs.setString('favorites', newFavoritesJson);
      AppLogger.success('已保存新收藏列表到SharedPreferences');

      if (!mounted) return;
      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('已添加到收藏库'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.green.shade800,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      AppLogger.error('添加收藏失败', error: e);
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('添加到收藏库失败，请重试'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red.shade800,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
