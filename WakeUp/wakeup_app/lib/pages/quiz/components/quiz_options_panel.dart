import 'package:flutter/cupertino.dart';
import '../../../models/quiz_model.dart';
import '../../../constants/fonts.dart';
import '../../../constants/quiz_constants.dart';

/// 问题选项面板组件
/// 处理不同类型问题的选项显示和交互
class QuizOptionsPanel extends StatelessWidget {
  final Question question;
  final int? selectedOptionId;
  final Function(int) onOptionSelected;
  final bool isInteractionEnabled;
  final QuestionInteractionState interactionState;

  const QuizOptionsPanel({
    super.key,
    required this.question,
    this.selectedOptionId,
    required this.onOptionSelected,
    this.isInteractionEnabled = true,
    this.interactionState = QuestionInteractionState.waitingForSelection,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 选项标题
          Text(
            '请选择答案：',
            style: AppFonts.createMixedStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: CupertinoColors.label,
            ),
          ),
          const SizedBox(height: 16),
          
          // 选项列表
          ...question.options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            return _buildOption(option, index);
          }),
        ],
      ),
    );
  }

  /// 构建单个选项
  Widget _buildOption(Option option, int index) {
    final isSelected = selectedOptionId == option.id;
    final showResult = interactionState == QuestionInteractionState.answerCorrect || 
                       interactionState == QuestionInteractionState.answerIncorrect;
    final isCorrect = option.isCorrect;
    
    Color backgroundColor;
    Color borderColor;
    Color textColor = CupertinoColors.label;
    
    if (showResult) {
      if (isCorrect) {
        backgroundColor = CupertinoColors.systemGreen.withValues(alpha: 0.1);
        borderColor = CupertinoColors.systemGreen;
        textColor = CupertinoColors.systemGreen;
      } else if (isSelected && !isCorrect) {
        backgroundColor = CupertinoColors.systemRed.withValues(alpha: 0.1);
        borderColor = CupertinoColors.systemRed;
        textColor = CupertinoColors.systemRed;
      } else {
        backgroundColor = CupertinoColors.systemGrey6;
        borderColor = CupertinoColors.systemGrey4;
      }
    } else {
      if (isSelected) {
        backgroundColor = CupertinoColors.systemBlue.withValues(alpha: 0.1);
        borderColor = CupertinoColors.systemBlue;
        textColor = CupertinoColors.systemBlue;
      } else {
        backgroundColor = CupertinoColors.systemBackground;
        borderColor = CupertinoColors.systemGrey4;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: isInteractionEnabled && !showResult 
            ? () => onOptionSelected(option.id) 
            : null,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: borderColor, width: 1.5),
          ),
          child: Row(
            children: [
              // 选项标号
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: isSelected || (showResult && isCorrect) 
                      ? borderColor 
                      : CupertinoColors.systemGrey5,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D...
                    style: AppFonts.createMixedStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected || (showResult && isCorrect)
                          ? CupertinoColors.white
                          : CupertinoColors.systemGrey,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              
              // 选项内容
              Expanded(
                child: Text(
                  option.content,
                  style: AppFonts.createMixedStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w400,
                    color: textColor,
                    height: 1.4,
                  ),
                ),
              ),
              
              // 结果图标
              if (showResult) ...[
                const SizedBox(width: 8),
                Icon(
                  isCorrect 
                      ? CupertinoIcons.checkmark_circle_fill
                      : isSelected 
                          ? CupertinoIcons.xmark_circle_fill
                          : null,
                  color: isCorrect 
                      ? CupertinoColors.systemGreen
                      : CupertinoColors.systemRed,
                  size: 20,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

