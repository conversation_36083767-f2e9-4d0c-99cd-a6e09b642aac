import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:wakeup_app/l10n/app_localizations.dart';
import 'package:wakeup_app/core/utils/logger.dart';

import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../course/course_page.dart';
import 'quiz_home_page.dart' as quiz_home;
import '../../providers/api_cache_provider.dart';
import '../../widgets/glass_bottom_navigation_bar.dart';
import '../../widgets/auth_wrapper.dart';
import '../knowledge/knowledge_card_page.dart';
import '../resources/resource_library_page.dart';
import '../../widgets/avatar_animation_scope.dart';

class MainPage extends StatefulWidget {
  final int initialIndex;

  const MainPage({super.key, this.initialIndex = 0});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  late int _currentIndex;
  // bool _isLoggedIn = false;

  // 添加一个key来强制重建LearningPage
  Key _learningPageKey = UniqueKey();

  // 头像动画一次性触发 token（每次切换 Tab 时递增）
  int _avatarPlayToken = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex.clamp(0, 3); // 限制索引范围为0-3
    _loadDataInBackground();
  }

  @override
  void dispose() {
    // 确保在组件销毁时清理任何未完成的异步操作
    // 注意：Future本身不能被取消，但我们通过mounted检查来避免setState调用
    super.dispose();
  }

  // 后台数据加载
  void _loadDataInBackground() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final apiCacheProvider = Provider.of<ApiCacheProvider>(
      context,
      listen: false,
    );

    if (!userProvider.isLoggedIn) {
      return;
    }

    Map<String, String> headers = {
      'Authorization': 'Bearer ${userProvider.token}',
    };

    try {
      // 加载高优先级数据
      await _loadPrimaryData(apiCacheProvider, headers);

      // 加载低优先级数据
      await _loadSecondaryData(apiCacheProvider, headers);
    } catch (e) {
      AppLogger.error('MainPage 背景加载出错', error: e);
    }
  }

  // 加载高优先级数据（用户资料、课程）
  Future<void> _loadPrimaryData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      // 并行加载用户资料和课程数据
      await Future.wait([
        apiCacheProvider.getWithCache(
          '${AuthService.baseUrl}/api/profile',
          headers: headers,
          useMemoryCache: true,
        ),
        apiCacheProvider.getWithCache(
          '${AuthService.baseUrl}/api/courses',
          headers: headers,
          useMemoryCache: true,
        ),
      ]);
    } catch (e) {
      AppLogger.error('主要数据加载错误', error: e);
    }
  }

  // 加载低优先级数据（推荐）
  Future<void> _loadSecondaryData(
    ApiCacheProvider apiCacheProvider,
    Map<String, String> headers,
  ) async {
    try {
      await apiCacheProvider.getWithCache(
        '${AuthService.baseUrl}/api/recommendations',
        headers: headers,
        useMemoryCache: true,
      );
    } catch (e) {
      AppLogger.error('推荐数据加载错误', error: e);
    }
  }

  // 导航到指定页面
  void _navigateToPage(int index) {
    // 如果点击的是学习页面(index=0)，总是强制刷新数据
    if (index == 0) {
      AppLogger.info('点击学习页面，强制刷新课程数据');
      // 生成新的key来强制重建LearningPage
      if (mounted) {
        setState(() {
          _learningPageKey = UniqueKey();
          _currentIndex = index;
        });
      }

      // 同时重置UserProvider中的课程变化标志
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      if (userProvider.courseChanged) {
        userProvider.resetCourseChanged();
      }
      return;
    }

    if (mounted) {
      setState(() {
        _currentIndex = index;
        _avatarPlayToken++; // 切换 Tab 时递增，触发新 Tab 下头像播放一次
      });
    }
  }

  // 使用getter来动态生成页面列表，确保LearningPage使用最新的key
  List<Widget> get _pages => [
    quiz_home.LearningPage(key: _learningPageKey),
    CoursePage(),
    KnowledgeCardPage(),
    ResourceLibraryPage(),
  ];

  @override
  Widget build(BuildContext context) {
    // 使用 AuthWrapper 包装整个页面
    return AuthWrapper(
      child: Scaffold(
        body: Stack(
          children: [
            // 主要内容区域（仅当前 Tab 允许动画 tick，且下发一次性播放 token）
            TickerMode(
              enabled: true, // 对于根部的动画（若有），保持开启
              child: AvatarAnimationScope(
                playToken: _avatarPlayToken,
                child: IndexedStack(
                  index: _currentIndex,
                  children: List.generate(_pages.length, (i) {
                    return TickerMode(
                      enabled: i == _currentIndex,
                      child: _pages[i],
                    );
                  }),
                ),
              ),
            ),

            // 底部导航栏作为悬浮元素
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              // 添加zIndex确保导航栏始终在最上层
              child: Container(
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -1),
                    ),
                  ],
                ),
                child: GlassBottomNavigationBar(
                  items: [
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.bubble_left_bubble_right),
                      activeIcon: const Icon(
                        CupertinoIcons.bubble_left_bubble_right_fill,
                      ),
                      label: AppLocalizations.of(context)!.navLearning,
                    ),
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.square_list),
                      activeIcon: const Icon(CupertinoIcons.square_list_fill),
                      label: AppLocalizations.of(context)!.navCourses,
                    ),
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.news),
                      activeIcon: const Icon(CupertinoIcons.news_solid),
                      label: AppLocalizations.of(context)!.navInformation,
                    ),
                    BottomNavigationBarItem(
                      icon: const Icon(CupertinoIcons.folder),
                      activeIcon: const Icon(CupertinoIcons.folder_fill),
                      label: AppLocalizations.of(context)!.navResources,
                    ),
                  ],
                  currentIndex: _currentIndex,
                  onTap: _navigateToPage,
                  blur: 10.0,
                  opacity: 0.3,
                  darkMode: true,
                  height: 52.0,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
