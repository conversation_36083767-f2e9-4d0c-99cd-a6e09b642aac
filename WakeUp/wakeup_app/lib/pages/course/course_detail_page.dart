import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'third_level_page.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import '../../services/auth_service.dart';
import '../../constants/fonts.dart';
// import 'package:wakeup_app/l10n/app_localizations.dart';
import '../../widgets/back_button.dart';
// import '../main/main_page.dart';
// import '../auth/login_page.dart';
// import 'package:provider/provider.dart';
// import '../../providers/user_provider.dart';

class CourseDetailPage extends StatefulWidget {
  final String courseName;
  final String description;
  final String highlight;
  final int categoryId;

  const CourseDetailPage({
    super.key,
    required this.courseName,
    required this.description,
    required this.highlight,
    required this.categoryId,
  });

  @override
  State<CourseDetailPage> createState() => _CourseDetailPageState();
}

class _CourseDetailPageState extends State<CourseDetailPage> {
  late Future<List<dynamic>> thirdLevelCategories;

  @override
  void initState() {
    super.initState();
    thirdLevelCategories = fetchThirdLevelCategories(widget.categoryId);
  }

  // 获取三级分类
  Future<List<dynamic>> fetchThirdLevelCategories(int categoryId) async {
    try {
      final response = await http.get(
        Uri.parse(
          "${AuthService.baseUrl}/api/categories/level3?parent_id=$categoryId&_t=${DateTime.now().millisecondsSinceEpoch}",
        ),
      );
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        // 从响应中提取 data 字段，这是包含分类列表的数组
        if (jsonResponse is Map && jsonResponse.containsKey('data')) {
          return jsonResponse['data'];
        } else {
          // 如果响应不包含 data 字段，可能是旧版 API 直接返回数组
          return jsonResponse;
        }
      } else {
        // API无法访问或返回错误，使用测试占位
        debugPrint('❌ 获取三级分类API失败，使用测试占位: ${response.statusCode}');
        return [
          {
            "id": categoryId * 10 + 1,
            "name": "Python编程（测试）",
            "description": "学习Python编程语言（测试）",
          },
          {
            "id": categoryId * 10 + 2,
            "name": "Java编程（测试）",
            "description": "掌握Java开发技能（测试）",
          },
          {
            "id": categoryId * 10 + 3,
            "name": "Web开发（测试）",
            "description": "前端与后端Web技术（测试）",
          },
        ];
      }
    } catch (e) {
      // 网络异常，使用测试占位
      debugPrint('❌ 获取三级分类错误，使用测试占位: $e');
      return [
        {
          "id": categoryId * 10 + 1,
          "name": "Python编程（测试）",
          "description": "学习Python编程语言（测试）",
        },
        {
          "id": categoryId * 10 + 2,
          "name": "Java编程（测试）",
          "description": "掌握Java开发技能（测试）",
        },
        {
          "id": categoryId * 10 + 3,
          "name": "Web开发（测试）",
          "description": "前端与后端Web技术（测试）",
        },
      ];
    }
  }

  // Copied from favorites_page.dart and adapted
  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          BackButtonWidget(
            text: "",
            color: Colors.white,
            onPressed: () => Navigator.of(context).pop(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              widget.courseName, // Dynamic title
              style:
                  Platform.isIOS &&
                          Localizations.localeOf(context).languageCode != 'zh'
                      ? TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      )
                      : AppFonts.createTitleStyle(
                        fontSize: 18,
                        isLatinText:
                            Localizations.localeOf(context).languageCode !=
                            'zh',
                      ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(width: 34 + 16), // To balance the back button
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        fit: StackFit.expand,
        children: [
          // 替换CustomPaint背景为图片背景
          Positioned.fill(
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Image.asset(
                'assets/images/traditional_quiz_bg.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  debugPrint('❌ 背景图片加载失败: $error');
                  // 出错时使用备选背景
                  return Image.asset(
                    'assets/images/learning_bg3.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  );
                },
              ),
            ),
          ),
          // 使用SafeArea和内容结构与学习页面保持一致
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildAppBar(context), // Call the new AppBar here
                // 添加顶部间距
                const SizedBox(height: 16),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 二级分类名称
                        Text(
                          widget.courseName,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 12),
                        // 二级分类描述
                        Text(
                          widget.description,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 12),
                        // 可能是激励信息
                        Text(
                          widget.highlight,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 20),
                        // 为"选择你想要学习的课程"部分添加渐变背景
                        Container(
                          width: double.infinity, // 背景宽度占满整个屏幕
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [
                                Color(0xFF5A00C0),
                                Color(0xFF00B4DB),
                              ], // 蓝紫渐变
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '选择你想要学习的课程',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white, // 使文本为白色以便与渐变背景形成对比
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        // 显示三级分类
                        Expanded(
                          child: FutureBuilder<List<dynamic>>(
                            future: thirdLevelCategories,
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.waiting) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              }
                              if (snapshot.hasError) {
                                return Center(
                                  child: Text('加载失败: ${snapshot.error}'),
                                );
                              }

                              final data = snapshot.data!;
                              return ListView(
                                children:
                                    data.map((item) {
                                      return GestureDetector(
                                        onTap: () {
                                          Navigator.of(context).push(
                                            MaterialPageRoute(
                                              builder:
                                                  (_) => ThirdLevelPage(
                                                    secondLevelId: int.parse(
                                                      item['id'].toString(),
                                                    ),
                                                    secondLevelName:
                                                        item['name'],
                                                  ),
                                            ),
                                          );
                                        },
                                        child: Container(
                                          margin: const EdgeInsets.symmetric(
                                            vertical: 6.0,
                                          ),
                                          child: ClipRRect(
                                            borderRadius: BorderRadius.circular(
                                              16.0,
                                            ),
                                            child: BackdropFilter(
                                              filter: ImageFilter.blur(
                                                sigmaX: 7.0,
                                                sigmaY: 7.0,
                                              ),
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 16.0,
                                                      vertical: 14.0,
                                                    ),
                                                decoration: BoxDecoration(
                                                  color: Colors.white
                                                      .withValues(alpha: 0.12),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        16.0,
                                                      ),
                                                  border: Border.all(
                                                    color: Colors.white
                                                        .withValues(alpha: 0.2),
                                                    width: 1.0,
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Expanded(
                                                      child: Text(
                                                        item['name'],
                                                        style: const TextStyle(
                                                          fontSize: 15,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color: Colors.white,
                                                        ),
                                                        overflow:
                                                            TextOverflow
                                                                .ellipsis,
                                                      ),
                                                    ),
                                                    const Icon(
                                                      CupertinoIcons
                                                          .chevron_forward,
                                                      size: 18,
                                                      color: Colors.white70,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    }).toList(),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white10
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;

    final path = Path();
    for (int i = 0; i < 6; i++) {
      final startX = size.width * (-0.1 + 0.22 * i);
      path.moveTo(startX, 0);
      path.cubicTo(
        startX + size.width * 0.4,
        size.height * 0.25,
        startX - size.width * 0.15,
        size.height * 0.75,
        startX + size.width * 0.35,
        size.height,
      );
    }
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
