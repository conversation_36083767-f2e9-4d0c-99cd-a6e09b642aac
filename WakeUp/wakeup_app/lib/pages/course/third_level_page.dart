import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'dart:ui'; // Import for ImageFilter
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../services/auth_service.dart';
import '../../constants/fonts.dart';
import '../../widgets/back_button.dart';

import 'package:flutter/cupertino.dart';

import '../../core/routing/app_router.dart';

class ThirdLevelPage extends StatefulWidget {
  final int secondLevelId;
  final String secondLevelName;

  const ThirdLevelPage({
    super.key,
    required this.secondLevelId,
    required this.secondLevelName,
  });

  @override
  State<ThirdLevelPage> createState() => _ThirdLevelPageState();
}

class _ThirdLevelPageState extends State<ThirdLevelPage> {
  late Future<List<dynamic>> fourthLevelCategories;
  // 存储课程状态，记录哪些课程已经加入
  Map<int, bool> courseStatus = {};
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initPageData();
  }

  void _initPageData() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (userProvider.isLoggedIn) {
      // 如果用户已登录，先获取用户已加入的课程
      await _fetchUserCourses(userProvider.userId, userProvider.token ?? "");
    }
    // 然后获取四级分类数据
    fourthLevelCategories = fetchFourthLevelCategories(widget.secondLevelId);
    if (mounted) setState(() {}); // Ensure UI updates after async operations
  }

  // 获取用户已加入的课程列表
  Future<void> _fetchUserCourses(int userId, String token) async {
    try {
      setState(() {
        isLoading = true;
      });

      final response = await http.get(
        Uri.parse("${AuthService.baseUrl}/api/user_course/list/$userId"),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> courses = jsonDecode(response.body);
        // 创建一个Map存储用户已加入的课程ID
        final Map<int, bool> newStatus = {};
        for (var course in courses) {
          int courseId = course['course_id'];
          newStatus[courseId] = true;
        }

        setState(() {
          courseStatus = newStatus;
        });
      }
    } catch (e) {
      debugPrint('获取用户课程失败: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<List<dynamic>> fetchFourthLevelCategories(int categoryId) async {
    try {
      final response = await http.get(
        Uri.parse(
          "${AuthService.baseUrl}/api/categories/level4?parent_id=$categoryId&_t=${DateTime.now().millisecondsSinceEpoch}",
        ),
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        // API不存在或访问失败，使用测试占位
        debugPrint('❌ 获取四级分类API失败，使用测试占位: ${response.statusCode}');
        return [
          {
            "id": categoryId * 100 + 1,
            "name": "Python基础课程（测试）",
            "description": "适合零基础学习Python的入门课程（测试）",
          },
          {
            "id": categoryId * 100 + 2,
            "name": "Java基础课程（测试）",
            "description": "Java语言核心概念与编程实践（测试）",
          },
          {
            "id": categoryId * 100 + 3,
            "name": "Web前端开发（测试）",
            "description": "HTML, CSS, JavaScript基础到进阶（测试）",
          },
        ];
      }
    } catch (e) {
      // 网络异常，使用测试占位
      debugPrint('❌ 获取四级分类错误，使用测试占位: $e');
      return [
        {
          "id": categoryId * 100 + 1,
          "name": "Python基础课程（测试）",
          "description": "适合零基础学习Python的入门课程（测试）",
        },
        {
          "id": categoryId * 100 + 2,
          "name": "Java基础课程（测试）",
          "description": "Java语言核心概念与编程实践（测试）",
        },
        {
          "id": categoryId * 100 + 3,
          "name": "Web前端开发（测试）",
          "description": "HTML, CSS, JavaScript基础到进阶（测试）",
        },
      ];
    }
  }

  Future<void> addCourseToBackend(
    Map<String, dynamic> course,
    int userId,
    String token,
  ) async {
    final response = await http.post(
      Uri.parse("${AuthService.baseUrl}/api/user_course/join"),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $token",
      },
      body: jsonEncode({
        "user_id": userId,
        "course_id": course["id"],
        "course_name": course["name"],
        "description": course["description"] ?? "暂无描述",
        "cover_image": "assets/images/learning_bg${course["id"] % 3 + 1}.png",
        "english_name": course["english_name"],
      }),
    );
    if (response.statusCode != 200 && response.statusCode != 201) {
      throw Exception("加入题库失败: ${response.statusCode}");
    }

    // 更新状态
    if (!mounted) return;
    setState(() {
      courseStatus[course["id"]] = true;
    });

    // 通知UserProvider课程数据已变化
    if (!mounted) return;
    Provider.of<UserProvider>(context, listen: false).notifyCourseChange();
  }

  Future<void> leaveCourseBackend(
    Map<String, dynamic> course,
    int userId,
    String token,
  ) async {
    final response = await http.post(
      Uri.parse("${AuthService.baseUrl}/api/user_course/leave"),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $token",
      },
      body: jsonEncode({"user_id": userId, "course_id": course["id"]}),
    );
    if (response.statusCode != 200) {
      throw Exception("退出题库失败: ${response.statusCode}");
    }

    // 更新状态
    if (!mounted) return;
    setState(() {
      courseStatus[course["id"]] = false;
    });

    // 通知UserProvider课程数据已变化
    if (!mounted) return;
    Provider.of<UserProvider>(context, listen: false).notifyCourseChange();
  }

  // 显示删除确认对话框，基于传统答题页面的样式
  Future<bool?> _showDeleteConfirmation(BuildContext context) async {
    double screenWidth = MediaQuery.of(context).size.width;
    double bottomPadding = MediaQuery.of(context).padding.bottom;

    return showModalBottomSheet<bool>(
      context: context,
      backgroundColor: Colors.transparent,
      isDismissible: true,
      builder: (BuildContext context) {
        // 构建操作项样式辅助函数
        Widget buildActionItem(
          String text,
          VoidCallback onPressed, {
          bool isDestructive = false,
          FontWeight fontWeight = FontWeight.normal,
        }) {
          return GestureDetector(
            onTap: onPressed,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              alignment: Alignment.center,
              child: Text(
                text,
                style: TextStyle(
                  color: const Color(0xFFE0464C), // 统一使用自定义红色
                  fontSize: 17.0, // 统一字体大小
                  fontWeight: fontWeight,
                ),
              ),
            ),
          );
        }

        return Padding(
          // 外层Padding，用于屏幕边缘和底部安全区域
          padding: EdgeInsets.only(
            left: 8.0,
            right: 8.0,
            bottom: bottomPadding > 0 ? bottomPadding : 8.0,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end, // 整体底部对齐
            children: [
              // ----- 标题和主要操作区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15.0,
                    sigmaY: 15.0,
                  ), // AppleStyleMenu模糊
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withValues(alpha: 0.92), // AppleStyleMenu背景色
                      // borderRadius已由ClipRRect处理
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 标题
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 16.0,
                            horizontal: 20.0,
                          ),
                          child: Text(
                            "确定要退出题库吗？",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: Color(0xFFE5E5E7),
                              fontSize: 13.0,
                              fontWeight: FontWeight.bold, // 标题加粗
                            ),
                          ),
                        ),
                        // 分隔线
                        Divider(
                          height: 0.5,
                          thickness: 0.5,
                          color: Colors.grey[700]?.withValues(alpha: 0.5),
                        ),
                        // "退出题库" 按钮
                        buildActionItem(
                          "退出题库",
                          () => Navigator.of(context).pop(true),
                          fontWeight: FontWeight.bold, // "退出题库"按钮加粗
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8.0), // 主要操作区和取消按钮之间的间距
              // ----- 取消按钮区域 -----
              ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15.0,
                    sigmaY: 15.0,
                  ), // AppleStyleMenu模糊
                  child: Container(
                    width: screenWidth - 16.0, // 适应屏幕宽度减去左右padding
                    decoration: BoxDecoration(
                      color: const Color(
                        0xFF1C1C1E,
                      ).withValues(alpha: 0.92), // AppleStyleMenu背景色
                      // borderRadius已由ClipRRect处理
                    ),
                    child: buildActionItem(
                      "取消",
                      () => Navigator.of(context).pop(false),
                      fontWeight: FontWeight.bold, // "取消"按钮加粗
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> showConfirmDialog(
    Map<String, dynamic> course,
    BuildContext context,
  ) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    // 检查用户是否登录
    if (!userProvider.isLoggedIn) {
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('需要登录'),
              content: const Text('请先登录以加入题库'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    AppRouter.pushLogin(context).then((_) {
                      // 登录后重新加载数据
                      if (!context.mounted) return;
                      final up = Provider.of<UserProvider>(
                        context,
                        listen: false,
                      );
                      if (up.isLoggedIn) {
                        _initPageData();
                      }
                    });
                  },
                  child: const Text('去登录'),
                ),
              ],
            ),
      );
      return;
    }

    // 判断是加入还是退出
    bool isJoined = courseStatus[course["id"]] ?? false;

    if (isJoined) {
      // 退出题库 - 先显示确认对话框
      final confirmed = await _showDeleteConfirmation(context);

      if (confirmed == true) {
        try {
          await leaveCourseBackend(
            course,
            userProvider.userId,
            userProvider.token ?? "",
          );

          // 注意：这里不再显示退出成功的提示
        } catch (e) {
          if (context.mounted) {
            _showErrorNotification("退出失败: $e");
          }
        }
      }
    } else {
      // 加入题库 - 直接执行不需要确认
      try {
        await addCourseToBackend(
          course,
          userProvider.userId,
          userProvider.token ?? "",
        );

        if (context.mounted) {
          showSuccessDialog();
          // 加入成功后直接导航到答题页面
          _navigateToQuizPage(course);
        }
      } catch (e) {
        if (context.mounted) {
          _showErrorNotification("加入失败: $e");
        }
      }
    }
  }

  Future<void> showSuccessDialog() async {
    _showSuccessToast("已成功加入题库");
  }

  // 已被新的提示方法替代（保留兼容调用点）。
  // ignore: unused_element
  void _showFeedbackDialog(String message) {
    if (message.contains('已退出') || message.contains('失败')) {
      _showErrorNotification(message);
    } else {
      _showSuccessToast(message);
    }
  }

  // 新增：构建课程操作按钮
  Widget _buildCourseActionButton(
    Map<String, dynamic> item,
    bool isCourseAdded,
  ) {
    if (isCourseAdded) {
      // "答题" 按钮 - 新设计
      return ElevatedButton.icon(
        icon: Icon(
          CupertinoIcons.play_arrow_solid,
          size: 18,
          color: Colors.white,
        ),
        label: Text(
          '答题',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
        onPressed: () => _navigateToQuizPage(item),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.lightBlueAccent, // 蓝色背景表示可以答题
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ), // 胶囊形状
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
          elevation: 3, // 较高的视觉层级
          shadowColor: Colors.black.withValues(alpha: 0.5),
        ),
      );
    } else {
      // "加入学习" 按钮 - 新设计
      return ElevatedButton.icon(
        icon: Icon(
          CupertinoIcons.add,
          size: 18,
          color: Colors.black.withValues(alpha: 0.75),
        ),
        label: Text(
          '加入题库',
          style: TextStyle(
            color: Colors.black.withValues(alpha: 0.75),
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
        onPressed: () => showConfirmDialog(item, context),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.lightBlueAccent, // 改为浅蓝色
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ), // 胶囊形状
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
          elevation: 3, // 较高的视觉层级
          shadowColor: Colors.black.withValues(alpha: 0.5),
        ),
      );
    }
  }

  Widget _buildAppBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          BackButtonWidget(
            text: "",
            color: Colors.white,
            onPressed: () => Navigator.of(context).pop(),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              widget.secondLevelName,
              style:
                  Platform.isIOS &&
                          Localizations.localeOf(context).languageCode != 'zh'
                      ? TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.7),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      )
                      : AppFonts.createTitleStyle(
                        fontSize: 18,
                        isLatinText:
                            Localizations.localeOf(context).languageCode !=
                            'zh',
                      ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(width: 34 + 16), // To balance the back button
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        fit: StackFit.expand,
        children: [
          // 背景图片
          Positioned.fill(
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Image.asset(
                'assets/images/traditional_quiz_bg.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorBuilder: (context, error, stackTrace) {
                  debugPrint('❌ 背景图片加载失败: $error');
                  // 出错时使用备选背景
                  return Image.asset(
                    'assets/images/learning_bg3.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                  );
                },
              ),
            ),
          ),
          // 内容区域
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 应用收藏库样式的顶部导航栏
                _buildAppBar(context),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF5A00C0), Color(0xFF00B4DB)],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      '选择你想要学习的课程',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                if (isLoading)
                  const Center(child: CircularProgressIndicator())
                else
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: FutureBuilder<List<dynamic>>(
                        future: fourthLevelCategories,
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          }
                          if (snapshot.hasError) {
                            return Center(
                              child: Text('加载失败: ${snapshot.error}'),
                            );
                          }

                          final data = snapshot.data!;
                          return ListView.builder(
                            itemCount: data.length,
                            itemBuilder: (context, index) {
                              final item = data[index];
                              final courseId = item["id"];
                              final bool isCourseAdded =
                                  courseStatus[courseId] ?? false;

                              // 新的卡片设计 - 让已加入的课程可以点击进入答题
                              return Container(
                                margin: const EdgeInsets.symmetric(
                                  vertical: 8.0,
                                ),
                                child: GestureDetector(
                                  onTap:
                                      isCourseAdded
                                          ? () => _navigateToQuizPage(item)
                                          : null,
                                  onLongPress:
                                      isCourseAdded
                                          ? () => _showCourseOptions(item)
                                          : null,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(16.0),
                                    child: BackdropFilter(
                                      filter: ImageFilter.blur(
                                        sigmaX: 7.0,
                                        sigmaY: 7.0,
                                      ),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16.0,
                                          vertical: 12.0,
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              isCourseAdded
                                                  ? Colors.white.withValues(
                                                    alpha: 0.18,
                                                  ) // 已加入的课程背景稍亮
                                                  : Colors.white.withValues(
                                                    alpha: 0.12,
                                                  ),
                                          borderRadius: BorderRadius.circular(
                                            16.0,
                                          ),
                                          border: Border.all(
                                            color:
                                                isCourseAdded
                                                    ? Colors.lightBlueAccent
                                                        .withValues(
                                                          alpha: 0.5,
                                                        ) // 已加入的课程有蓝色边框
                                                    : Colors.white.withValues(
                                                      alpha: 0.2,
                                                    ),
                                            width: 1.0,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Expanded(
                                                        child: Text(
                                                          item['name'],
                                                          style:
                                                              const TextStyle(
                                                                fontSize: 15,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color:
                                                                    Colors
                                                                        .white,
                                                              ),
                                                          maxLines: 2,
                                                          overflow:
                                                              TextOverflow
                                                                  .ellipsis,
                                                        ),
                                                      ),
                                                      if (isCourseAdded) ...[
                                                        const SizedBox(
                                                          width: 8,
                                                        ),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets.symmetric(
                                                                horizontal: 8,
                                                                vertical: 4,
                                                              ),
                                                          decoration: BoxDecoration(
                                                            color: Colors
                                                                .lightBlueAccent
                                                                .withValues(
                                                                  alpha: 0.8,
                                                                ),
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  12,
                                                                ),
                                                          ),
                                                          child: const Text(
                                                            '点击答题',
                                                            style: TextStyle(
                                                              fontSize: 10,
                                                              color:
                                                                  Colors.white,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ],
                                                  ),
                                                  if (item['description'] !=
                                                          null &&
                                                      item['description']
                                                          .toString()
                                                          .isNotEmpty) ...[
                                                    const SizedBox(height: 6),
                                                    Text(
                                                      item['description'],
                                                      style: TextStyle(
                                                        fontSize: 13,
                                                        color: Colors.white
                                                            .withValues(
                                                              alpha: 0.75,
                                                            ),
                                                        height: 1.3,
                                                      ),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ],
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            _buildCourseActionButton(
                                              item,
                                              isCourseAdded,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 显示成功提示的浮动消息
  void _showSuccessToast(String message) {
    if (!mounted) return;

    // 创建覆盖层
    final overlay = Overlay.of(context);

    // 创建覆盖层条目
    OverlayEntry overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            bottom: MediaQuery.of(context).padding.bottom + 30, // 调整到底部，并增加一些边距
            left: 0,
            right: 0,
            child: Center(
              // 使提示框水平居中
              child: Material(
                color: Colors.transparent, // Material 本身透明
                child: ClipRRect(
                  // 用于实现圆角毛玻璃
                  borderRadius: BorderRadius.circular(10), // 统一圆角
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 15,
                      sigmaY: 15,
                    ), // 匹配AppleStyleMenu的模糊参数
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 18,
                      ), // 调整水平和垂直内边距
                      decoration: BoxDecoration(
                        color: const Color(
                          0xFF1C1C1E,
                        ).withValues(alpha: 0.92), // 匹配AppleStyleMenu的颜色和透明度
                        borderRadius: BorderRadius.circular(10), // 统一圆角
                      ),
                      child: Row(
                        // 使用 Row 排列图标和文本
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min, // Row 的大小适应内容
                        children: [
                          const Icon(
                            // 使用实心填充图标
                            CupertinoIcons
                                .check_mark_circled_solid, // 更符合图片中的实心风格
                            color: Colors.white,
                            size: 16, // 调整图标大小以匹配文本视觉高度
                          ),
                          const SizedBox(width: 8), // 减小图标和文本之间的间距
                          Text(
                            message,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14, // 调整字体大小
                              fontWeight: FontWeight.w900, // 使文本更粗
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
    );

    // 插入覆盖层
    overlay.insert(overlayEntry);

    // 2秒后移除
    Future.delayed(const Duration(seconds: 2), () {
      overlayEntry.remove();
    });
  }

  // 显示错误通知对话框
  void _showErrorNotification(String message) {
    if (!context.mounted) return;
    // 使用CupertinoDialog而不是SnackBar
    showCupertinoDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        // 自动关闭对话框的定时器
        Future.delayed(const Duration(seconds: 2), () {
          if (!context.mounted) return;
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          }
        });

        return CupertinoAlertDialog(content: Text(message));
      },
    );
  }

  void _navigateToQuizPage(Map<String, dynamic> course) {
    AppRouter.toQuiz(
      context,
      courseId: course['id'],
      courseName: course['name'],
    );
  }

  void _showCourseOptions(Map<String, dynamic> course) {
    showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          title: Text(
            course['name'],
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          message: const Text('选择操作'),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _navigateToQuizPage(course);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Icon(
                    CupertinoIcons.play_arrow_solid,
                    color: CupertinoColors.systemBlue,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '开始答题',
                    style: TextStyle(color: CupertinoColors.systemBlue),
                  ),
                ],
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _showExitConfirmation(course);
              },
              isDestructiveAction: true,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Icon(
                    CupertinoIcons.minus_circle,
                    color: CupertinoColors.destructiveRed,
                  ),
                  SizedBox(width: 8),
                  Text(
                    '退出题库',
                    style: TextStyle(color: CupertinoColors.destructiveRed),
                  ),
                ],
              ),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        );
      },
    );
  }

  Future<void> _showExitConfirmation(Map<String, dynamic> course) async {
    final confirmed = await _showDeleteConfirmation(context);
    if (!mounted) return;
    if (confirmed == true) {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      try {
        await leaveCourseBackend(
          course,
          userProvider.userId,
          userProvider.token ?? "",
        );
        if (context.mounted) {
          _showSuccessToast("已退出题库");
        }
      } catch (e) {
        if (context.mounted) {
          _showErrorNotification("退出失败: $e");
        }
      }
    }
  }
}

class BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white10
          ..strokeWidth = 1
          ..style = PaintingStyle.stroke;

    final path = Path();
    for (int i = 0; i < 6; i++) {
      final startX = size.width * (-0.1 + 0.22 * i);
      path.moveTo(startX, 0);
      path.cubicTo(
        startX + size.width * 0.4,
        size.height * 0.25,
        startX - size.width * 0.15,
        size.height * 0.75,
        startX + size.width * 0.35,
        size.height,
      );
    }
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
