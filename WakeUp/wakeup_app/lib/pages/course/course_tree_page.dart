// lib/pages/course_tree_page.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/routing/app_router.dart';
import '../../providers/user_provider.dart';
// import '../auth/login_page.dart';
import '../../core/services/unified_api_service.dart';

class CourseTreePage extends StatefulWidget {
  final int courseId;
  final String courseName;

  const CourseTreePage({
    super.key,
    required this.courseId,
    required this.courseName,
  });

  @override
  State<CourseTreePage> createState() => _CourseTreePageState();
}

class _CourseTreePageState extends State<CourseTreePage> {
  bool isLoading = true;
  List<Map<String, dynamic>> chapterList = [];
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadCourseTree();
  }

  Future<void> _loadCourseTree() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    if (!userProvider.isLoggedIn) {
      setState(() {
        errorMessage = '请先登录';
        isLoading = false;
      });
      return;
    }

    setState(() {
      isLoading = true;
      errorMessage = '';
    });

    try {
      // 调用API获取课程章节树
      final response = await UnifiedApiService().get<List<dynamic>>(
        '/api/courses/${widget.courseId}/chapters',
        context: context,
        parser: (data) => data is List ? data : [data],
      );

      if (!response.isSuccess) {
        throw Exception(response.error?.message ?? '获取课程章节失败');
      }

      setState(() {
        chapterList = List<Map<String, dynamic>>.from(response.data!);
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = '加载失败: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);

    return Scaffold(
      appBar: AppBar(title: Text(widget.courseName), elevation: 0),
      body:
          !userProvider.isLoggedIn
              ? _buildLoginPrompt()
              : _buildCourseTreeContent(),
    );
  }

  Widget _buildLoginPrompt() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.lock, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          const Text('请登录后查看课程内容', style: TextStyle(fontSize: 18)),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              AppRouter.pushLogin(context).then((_) {
                if (!mounted) return;
                // 登录后重新加载
                if (Provider.of<UserProvider>(
                  context,
                  listen: false,
                ).isLoggedIn) {
                  _loadCourseTree();
                }
              });
            },
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  Widget _buildCourseTreeContent() {
    if (isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(errorMessage, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 24),
            ElevatedButton(onPressed: _loadCourseTree, child: const Text('重试')),
          ],
        ),
      );
    }

    if (chapterList.isEmpty) {
      return const Center(
        child: Text('暂无章节内容', style: TextStyle(fontSize: 16)),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: chapterList.length,
      itemBuilder: (context, index) {
        final chapter = chapterList[index];
        return _buildChapter(chapter);
      },
    );
  }

  Widget _buildChapter(Map<String, dynamic> chapter) {
    final items = chapter['items'] as List;

    // 计算当前章节完成进度
    final completedCount =
        items.where((item) => item['completed'] == true).length;
    final progress = items.isEmpty ? 0.0 : completedCount / items.length;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  chapter['title'],
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: LinearProgressIndicator(
                          value: progress,
                          backgroundColor: Colors.grey.shade800,
                          minHeight: 6,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              return _buildLessonItem(item);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLessonItem(Map<String, dynamic> item) {
    final IconData itemIcon;
    final Color itemColor;

    switch (item['type']) {
      case 'video':
        itemIcon = Icons.play_circle_outline;
        itemColor = Colors.blue;
        break;
      case 'quiz':
        itemIcon = Icons.quiz;
        itemColor = Colors.orange;
        break;
      case 'homework':
        itemIcon = Icons.assignment;
        itemColor = Colors.green;
        break;
      case 'project':
        itemIcon = Icons.psychology;
        itemColor = Colors.purple;
        break;
      default:
        itemIcon = Icons.article;
        itemColor = Colors.grey;
    }

    return ListTile(
      leading: Icon(
        itemIcon,
        color: item['completed'] == true ? Colors.green : itemColor,
      ),
      title: Text(
        item['title'],
        style: TextStyle(
          decoration:
              item['completed'] == true ? TextDecoration.lineThrough : null,
          color: item['completed'] == true ? Colors.grey : Colors.white,
        ),
      ),
      subtitle:
          item['duration'] != null ? Text('时长: ${item['duration']}') : null,
      trailing:
          item['completed'] == true
              ? const Icon(Icons.check_circle, color: Colors.green)
              : const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('正在开发: ${item['title']}')));
      },
    );
  }
}
