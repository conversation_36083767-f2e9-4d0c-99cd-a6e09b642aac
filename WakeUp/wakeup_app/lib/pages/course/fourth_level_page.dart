import 'package:flutter/material.dart';

class FourthLevelPage extends StatelessWidget {
  final int level4Id;
  final String level4Name;

  // 在构造函数中定义 level4Id 和 level4Name
  const FourthLevelPage({
    super.key,
    required this.level4Id,
    required this.level4Name,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(level4Name), // 显示四级分类名称
        backgroundColor: Colors.black,
      ),
      body: Center(
        child: Text(
          '四级分类ID: $level4Id\n四级分类名称: $level4Name',
          style: TextStyle(color: Colors.white, fontSize: 20),
        ),
      ),
    );
  }
}
