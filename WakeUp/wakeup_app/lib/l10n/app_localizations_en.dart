// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'WakeUP';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get verificationCode => 'Verification Code';

  @override
  String get sendCode => 'Send Code';

  @override
  String get resend => 'Resend';

  @override
  String get notification => 'Notification';

  @override
  String get confirm => 'Confirm';

  @override
  String get cancel => 'Cancel';

  @override
  String get pleaseEnterPhoneNumber => 'Please enter phone number';

  @override
  String get pleaseEnterVerificationCode =>
      'Please enter 6-digit verification code';

  @override
  String get codeSent => 'Verification code sent';

  @override
  String get loginSuccess => 'Login successful';

  @override
  String get loginFailed => '<PERSON><PERSON> failed, please try again later';

  @override
  String get loginRequired => 'Login Required';

  @override
  String get pleaseLoginFirst => 'Please login first to view course details';

  @override
  String get course => 'Course';

  @override
  String get courseDetails => 'Course Details';

  @override
  String get joinCourse => 'Join Course';

  @override
  String get joinCourseSuccess => 'Successfully joined the course';

  @override
  String get joinCourseFailed => 'Failed to join the course';

  @override
  String error(String error) {
    return 'Error: $error';
  }

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get english => 'English';

  @override
  String get chinese => 'Chinese';

  @override
  String get logout => 'Logout';

  @override
  String get logoutConfirmation => 'Are you sure you want to log out?';

  @override
  String get loadingFailedSimple => 'Loading failed';

  @override
  String loadingFailed(String error) {
    return 'Loading failed: $error';
  }

  @override
  String get unexpectedDataStructure =>
      'API returned unexpected data structure';

  @override
  String loadingCourseFailed(String error) {
    return 'Failed to load courses: $error';
  }

  @override
  String get coursePageTitle => 'Courses';

  @override
  String get whichCourseInterest => 'Which courses are you interested in?';

  @override
  String get courseSelectionHint =>
      'Please select one below, and we will recommend a learning path to help you start learning. (You can update this content at any time.)';

  @override
  String get clickCardForDetails => 'Click card for details';

  @override
  String loadingCourseError(String error) {
    return 'Failed to load: $error';
  }

  @override
  String get uncategorizedCourses => 'Uncategorized Courses';

  @override
  String get learningPageTitle => 'Quiz';

  @override
  String get pleaseLoginToLearn => 'Please log in to start learning';

  @override
  String get loginNow => 'Login Now';

  @override
  String get retry => 'Retry';

  @override
  String get noCourses => 'No courses joined yet';

  @override
  String get goToCoursePage => 'Go to Course Page';

  @override
  String get startLearning => 'Start Learning Now';

  @override
  String get selectCourseInterest =>
      'Please go to the course page to select courses you\'re interested in';

  @override
  String get myCourses => 'My Courses';

  @override
  String get informationHubPageTitle => 'Information Hub';

  @override
  String get resourceLibraryPageTitle => 'Resource Library';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get pushNotifications => 'Push Notifications';

  @override
  String get receiveNotifications =>
      'Receive learning reminders and important updates';

  @override
  String get languageSettings => 'Language Settings';

  @override
  String get privacySettings => 'Privacy Settings';

  @override
  String get findFriends => 'Find Friends';

  @override
  String get findFriendsDescription =>
      'Allow other users to find you via phone number or username';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get notLoggedIn => 'Not Logged In';

  @override
  String get setupProfile => 'Setup Profile';

  @override
  String get navLearning => 'Quiz';

  @override
  String get navCourses => 'Topics';

  @override
  String get navInformation => 'Knowledge';

  @override
  String get navResources => 'Resources';
}
