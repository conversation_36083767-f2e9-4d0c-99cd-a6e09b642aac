# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Flutter build outputs
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
build/

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# macOS files
.DS_Store

# Generated code (adjust if needed)
*.g.dart
*.freezed.dart

# Sensitive files
*.keystore
*.jks
key.properties
**/android/local.properties

# Logs and temporary files
*.log
*.tmp
*.bak

# iOS specific
ios/Pods/
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Flutter/.last_build_id
ios/Flutter/ephemeral/
ios/Flutter/Generated.xcconfig
ios/Flutter/app_framework_build.sh
ios/Runner.xcworkspace/xcshareddata/swiftpm/Package.resolved
ios/Runner.xcworkspace/xcuserdata/

# Android specific
android/.gradle/
android/app/build/
android/app/debug/
android/app/profile/
android/app/release/
android/gradle.properties # Sometimes contains sensitive info
android/key.properties # Often contains sensitive info
android/local.properties

# Web specific (if applicable)
web/build/

# Windows specific (if applicable)
windows/build/
windows/flutter/ephemeral/
windows/runner/Debug/
windows/runner/Profile/
windows/runner/Release/

# Linux specific (if applicable)
linux/build/
linux/flutter/ephemeral/

# Test outputs
coverage/
