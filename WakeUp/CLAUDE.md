# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

WakeUP是一个AI辅助学习应用，包含Flutter前端和Flask后端两个主要组件：

- **前端 (wakeup_app/)**: Flutter移动应用，支持Android和iOS
- **后端 (wakeup_backend/)**: Flask API服务器，使用SQLite数据库

## 开发命令

### Flutter前端开发
```bash
cd wakeup_app/

# 获取依赖
flutter pub get

# 运行应用（开发模式）
flutter run

# 构建应用
flutter build apk                    # Android APK
flutter build ios                    # iOS应用
flutter build appbundle             # Android App Bundle

# 代码分析和检查
flutter analyze                      # 静态代码分析
flutter test                        # 运行测试（如果有）

# 清理构建产物
flutter clean

# 生成应用图标
flutter pub run flutter_launcher_icons:generate
```

### Flask后端开发
```bash
cd wakeup_backend/

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
python app.py

# 数据库迁移
flask db init                       # 初始化迁移
flask db migrate -m "migration message"  # 创建迁移
flask db upgrade                     # 应用迁移

# 生产环境运行
gunicorn app:app
```

## 核心架构

### Flutter应用架构

**状态管理**: 使用Provider模式
- `UserProvider`: 用户状态管理
- `ApiCacheProvider`: API缓存管理
- `LocaleProvider`: 国际化语言管理
- `AppStateManager`: 全局应用状态管理

**路由系统**: 统一路由管理 (`core/routing/app_router.dart`)
- 类型安全的路由导航
- 集中管理所有页面路由

**数据层**:
- `services/`: API服务层，包含各种业务服务
- `models/`: 数据模型定义
- 多层缓存系统：内存缓存 + 持久化缓存

**页面结构**:
- `pages/main/`: 主要页面（首页、学习页面、个人页面等）
- `pages/auth/`: 认证相关页面
- `pages/course/`: 课程相关页面
- `pages/quiz/`: 测验系统页面

**共享组件**:
- `widgets/`: 可复用UI组件
- `shared/`: 共享工具和混入类

### Flask后端架构

**数据库模型** (`models/`):
- 用户系统：`user.py`, `user_course.py`, `user_answer.py`
- 课程系统：5级分类系统 (`category_level1-5.py`)
- 内容系统：`course.py`, `question.py`
- 地理系统：`region.py`, `region_category.py`

**API路由** (`routes/`):
- 认证：`auth_routes.py`
- 用户管理：`user_routes.py`
- 课程管理：`course_routes_new.py`
- 分类管理：`category_routes.py`

**工具类** (`utils/`):
- 认证工具：`auth.py`
- 数据库工具：`db_utils.py`
- 性能监控：`performance.py`

## 国际化支持

应用支持中文和英文：
- 配置文件：`l10n.yaml`
- 翻译文件：`lib/l10n/app_zh.arb`, `lib/l10n/app_en.arb`
- 字体支持：Noto Sans SC（中文）、Roboto（英文）

## 缓存策略

项目实现了多层次缓存系统：
- 内存缓存：快速访问常用数据
- 持久化缓存：离线访问能力
- API缓存：减少网络请求
- 并发请求优化：使用`Future.wait`并行加载

## 开发注意事项

1. **数据库迁移**: 修改模型后需要创建和应用迁移
2. **缓存清理**: 开发时可能需要清理缓存确保数据最新
3. **国际化**: 新增文本需要同时更新中英文翻译文件
4. **图标生成**: 修改应用图标后需要重新生成各平台图标
5. **依赖管理**: Flutter和Python依赖分别管理，注意版本兼容性