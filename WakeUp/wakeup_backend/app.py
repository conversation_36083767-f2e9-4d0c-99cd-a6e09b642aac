from flask import Flask, request
from flask_cors import CORS
from flask_migrate import Migrate
from flask_compress import Compress
from flask_caching import Cache
from models import db
from routes.user_course_routes import user_course_bp
# from routes.course_routes import course_bp
from routes.course_routes_new import course_new_bp
from routes.category_routes import category_bp
from routes.region_routes import region_bp
from routes.user_routes import user_bp
from routes.auth_routes import auth_bp
from routes.content_routes import content_bp
from routes.smart_hint_routes import smart_hint_bp
from utils.performance import init_performance_monitoring

app = Flask(__name__)
app.config.from_object('config.Config')
CORS(app)

# 配置 gzip 压缩
compress = Compress()
compress.init_app(app)
app.config['COMPRESS_MIMETYPES'] = [
    'text/html', 
    'text/css', 
    'text/xml',
    'application/json',
    'application/javascript'
]
app.config['COMPRESS_LEVEL'] = 6  # 压缩级别 (1-9)
app.config['COMPRESS_MIN_SIZE'] = 500  # 最小压缩大小 (字节)

# 配置缓存系统
cache_config = {
    'CACHE_TYPE': 'SimpleCache',  # 简单内存缓存，生产环境可以改为Redis
    'CACHE_DEFAULT_TIMEOUT': 300,  # 缓存过期时间（秒）
    'CACHE_THRESHOLD': 1000,       # 最大缓存项数
}
app.config.update(cache_config)
cache = Cache(app)

# 数据库初始化
db.init_app(app)
migrate = Migrate(app, db)

# 注册蓝图
app.register_blueprint(user_course_bp)
# app.register_blueprint(course_bp)  # 临时注释掉旧的课程路由
app.register_blueprint(course_new_bp)  # 新的课程路由
app.register_blueprint(category_bp)
app.register_blueprint(region_bp)  # 地区路由
app.register_blueprint(user_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(content_bp)  # 内容路由（推荐、活动等）
app.register_blueprint(smart_hint_bp)  # 智能提示路由

# 初始化性能监控
init_performance_monitoring(app)

# 添加缓存控制头
@app.after_request
def add_cache_headers(response):
    # 区分不同请求方法的缓存策略
    if request.method == 'GET':
        # 只为GET请求设置缓存
        response.headers['Cache-Control'] = 'public, max-age=300'  # 缓存5分钟
    else:
        # 非GET请求不缓存
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate'
    
    # 添加缓存相关头信息
    response.headers['Vary'] = 'Accept, Accept-Encoding, Origin'
    
    return response

# 增加响应速度配置
@app.route('/api/config', methods=['GET'])
@cache.cached(timeout=3600)  # 缓存1小时
def get_config():
    return {
        'api_version': '1.0.0',
        'max_response_time': 300,  # 最大响应时间（毫秒）
        'compression_enabled': True,
        'pagination_enabled': True,
        'caching_enabled': True
    }

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
