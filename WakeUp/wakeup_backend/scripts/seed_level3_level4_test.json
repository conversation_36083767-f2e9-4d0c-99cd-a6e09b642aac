{"category_level3": [{"id": 101, "name": "Python编程基础", "english_name": "Python Basics", "level2_id": 1, "is_active": true}, {"id": 102, "name": "Java编程基础", "english_name": "Java Basics", "level2_id": 1, "is_active": true}, {"id": 103, "name": "Web核心技术", "english_name": "Web Core", "level2_id": 5, "is_active": true}], "category_level4": [{"id": 1001, "name": "Python入门题库", "english_name": "Python Intro", "level3_id": 101, "is_active": true}, {"id": 1002, "name": "Python数据结构", "english_name": "Python DS", "level3_id": 101, "is_active": true}, {"id": 1003, "name": "Java语法题库", "english_name": "Java Syntax", "level3_id": 102, "is_active": true}, {"id": 1004, "name": "前端基础题库", "english_name": "Frontend Basics", "level3_id": 103, "is_active": true}]}