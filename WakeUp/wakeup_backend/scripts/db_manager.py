#!/usr/bin/env python3
"""
统一的数据库管理工具
替换原有的45个重复数据库脚本，提供一站式数据库管理功能
"""

import sys
import os
import argparse
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models import db
from models.user import User
from models.region import Region
from models.category_level1 import CategoryLevel1
from models.category_level2 import CategoryLevel2
from models.category_level3 import CategoryLevel3
from models.category_level4 import CategoryLevel4
from models.category_level5 import CategoryLevel5
from models.course import Course
from models.user_course import UserCourse
from models.question import Question
from models.user_answer import UserAnswer
from models.region_category import RegionCategory

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///db.sqlite3'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, app):
        self.app = app
        self.db = db
        
    def init_database(self):
        """初始化数据库结构"""
        print("🔧 正在初始化数据库结构...")
        with self.app.app_context():
            # 创建所有表
            self.db.create_all()
            print("✅ 数据库表创建完成")
            
            # 创建索引
            self._create_indexes()
            print("✅ 数据库索引创建完成")
    
    def _create_indexes(self):
        """创建数据库索引以优化查询性能"""
        try:
            # 用户表索引
            self.db.engine.execute(
                "CREATE INDEX IF NOT EXISTS idx_user_username ON user(username)"
            )
            self.db.engine.execute(
                "CREATE INDEX IF NOT EXISTS idx_user_email ON user(email)"
            )
            
            # 问题表索引
            self.db.engine.execute(
                "CREATE INDEX IF NOT EXISTS idx_question_course ON question(course_id)"
            )
            self.db.engine.execute(
                "CREATE INDEX IF NOT EXISTS idx_question_set ON question(question_set_id)"
            )
            
            # 分类表索引
            self.db.engine.execute(
                "CREATE INDEX IF NOT EXISTS idx_category_active ON category_level1(is_active)"
            )
            
            print("📊 数据库索引创建完成")
        except Exception as e:
            print(f"⚠️  索引创建警告: {e}")
    
    def seed_data(self, data_file=None):
        """导入种子数据"""
        print("🌱 正在导入种子数据...")
        with self.app.app_context():
            if data_file and os.path.exists(data_file):
                self._import_from_file(data_file)
            else:
                self._create_default_data()
        print("✅ 种子数据导入完成")
    
    def _create_default_data(self):
        """创建默认数据"""
        # 创建默认地区
        if not Region.query.first():
            regions = [
                {'name': '中国', 'code': 'CN', 'is_active': True},
                {'name': '美国', 'code': 'US', 'is_active': True},
                {'name': '英国', 'code': 'UK', 'is_active': True},
            ]
            for region_data in regions:
                region = Region(**region_data)
                self.db.session.add(region)
        
        # 创建默认一级分类
        if not CategoryLevel1.query.first():
            categories = [
                {'name': '计算机科学', 'english_name': 'Computer Science', 'is_active': True},
                {'name': '数据科学', 'english_name': 'Data Science', 'is_active': True},
                {'name': 'Web开发', 'english_name': 'Web Development', 'is_active': True},
            ]
            for cat_data in categories:
                category = CategoryLevel1(**cat_data)
                self.db.session.add(category)
        
        self.db.session.commit()
    
    def _import_from_file(self, file_path):
        """从JSON文件导入数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 按依赖关系顺序导入数据
            import_order = [
                'users', 'regions', 'category_level1', 'category_level2', 
                'category_level3', 'category_level4', 'category_level5',
                'courses', 'user_courses', 'questions', 'user_answers', 'region_categories'
            ]
            
            for table_name in import_order:
                if table_name in data:
                    records = data[table_name]
                    model_class = self._get_model_class(table_name)
                    if model_class:
                        print(f"📥 导入 {table_name} 数据: {len(records)} 条记录")
                        for record in records:
                            # 检查记录是否已存在
                            existing = None
                            if 'id' in record:
                                existing = model_class.query.filter_by(id=record['id']).first()
                            
                            if existing:
                                print(f"   ⚠️  跳过已存在的记录 ID={record['id']}")
                                continue
                            
                            # 处理日期时间字段
                            record = self._process_datetime_fields(record, model_class)
                            
                            try:
                                instance = model_class(**record)
                                self.db.session.add(instance)
                                self.db.session.flush()  # 立即写入获取ID
                            except Exception as e:
                                print(f"   ❌ 插入记录失败: {record.get('id', 'Unknown')} - {e}")
                                # 只回滚这一条记录的操作，而不是整个会话
                                self.db.session.rollback()
                                # 但是继续处理其他记录
                                continue
            
            self.db.session.commit()
            print(f"✅ 从 {file_path} 导入数据完成")
        except Exception as e:
            print(f"❌ 导入数据失败: {e}")
            self.db.session.rollback()
    
    def _process_datetime_fields(self, record, model_class):
        """处理日期时间字段"""
        import datetime
        datetime_fields = [
            'created_at', 'updated_at', 'enrollment_date', 'completion_date', 
            'last_accessed', 'last_studied_at', 'started_at', 'completed_at'
        ]
        
        for field in datetime_fields:
            if field in record and record[field]:
                try:
                    if isinstance(record[field], str):
                        # 解析ISO格式日期时间
                        if 'T' in record[field]:
                            # 完整的ISO格式
                            record[field] = datetime.datetime.fromisoformat(record[field].replace('Z', '+00:00'))
                        else:
                            # 简单的日期格式
                            record[field] = datetime.datetime.strptime(record[field], '%Y-%m-%d')
                except Exception as e:
                    print(f"   ⚠️  日期时间字段 {field} 解析失败: {e}")
                    record[field] = None
        
        return record
    
    def _get_model_class(self, table_name):
        """根据表名获取模型类"""
        model_map = {
            'users': User,
            'regions': Region,
            'category_level1': CategoryLevel1,
            'category_level2': CategoryLevel2,
            'category_level3': CategoryLevel3,
            'category_level4': CategoryLevel4,
            'category_level5': CategoryLevel5,
            'courses': Course,
            'user_courses': UserCourse,
            'questions': Question,
            'user_answers': UserAnswer,
            'region_categories': RegionCategory,
        }
        return model_map.get(table_name)
    
    def backup_database(self, backup_path=None):
        """备份数据库"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        if not backup_path:
            backup_path = f"db_backup_{timestamp}.sqlite3"
        
        print(f"💾 正在备份数据库到 {backup_path}...")
        
        try:
            import shutil
            shutil.copy2('db.sqlite3', backup_path)
            print(f"✅ 数据库备份完成: {backup_path}")
        except Exception as e:
            print(f"❌ 备份失败: {e}")
    
    def analyze_database(self):
        """分析数据库状态"""
        print("📊 数据库分析报告")
        print("=" * 50)
        
        with self.app.app_context():
            tables = [
                ('用户', User),
                ('地区', Region),
                ('一级分类', CategoryLevel1),
                ('二级分类', CategoryLevel2),
                ('三级分类', CategoryLevel3),
                ('四级分类', CategoryLevel4),
                ('五级分类', CategoryLevel5),
                ('课程', Course),
                ('用户课程', UserCourse),
                ('问题', Question),
                ('用户答案', UserAnswer),
                ('地区分类关联', RegionCategory),
            ]
            
            for name, model in tables:
                try:
                    count = model.query.count()
                    print(f"{name:12} | {count:8} 条记录")
                except Exception as e:
                    print(f"{name:12} | 错误: {e}")
        
        print("=" * 50)
    
    def import_test_data(self):
        """导入临时登录用户的测试数据"""
        print("🧪 正在导入临时登录用户测试数据...")
        
        # 查找测试数据文件
        test_data_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'test_data.json')
        
        if not os.path.exists(test_data_file):
            print(f"❌ 测试数据文件不存在: {test_data_file}")
            return
        
        with self.app.app_context():
            # 初始化数据库结构（如果表不存在）
            self.db.create_all()
            print("✅ 数据库表结构检查完成")
            
            # 导入测试数据
            self._import_from_file(test_data_file)
            
        print("✅ 临时登录用户测试数据导入完成")
        print("📋 可使用以下凭据测试:")
        print("   用户ID: 888")
        print("   昵称: 测试用户")
        print("   Token: temp_skip_token_for_testing")

    def clean_database(self, confirm=False):
        """清理数据库"""
        if not confirm:
            print("⚠️  这将删除所有数据！请使用 --confirm 参数确认")
            return
        
        print("🧹 正在清理数据库...")
        with self.app.app_context():
            # 按依赖关系逆序删除，只删除存在的表
            from sqlalchemy import text
            tables_to_clean = [
                'user_course', 'courses', 'category_level2', 'category_level1', 'regions', 'user'
            ]
            
            for table_name in tables_to_clean:
                try:
                    self.db.session.execute(text(f"DELETE FROM {table_name}"))
                    print(f"✅ 清理表 {table_name}")
                except Exception as e:
                    print(f"⚠️ 跳过表 {table_name}: {e}")
            
            self.db.session.commit()
        print("✅ 数据库清理完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一数据库管理工具')
    parser.add_argument('command', choices=[
        'init', 'seed', 'backup', 'analyze', 'clean', 'test-data'
    ], help='要执行的命令')
    
    parser.add_argument('--data-file', help='种子数据文件路径')
    parser.add_argument('--backup-path', help='备份文件路径')
    parser.add_argument('--confirm', action='store_true', help='确认执行危险操作')
    
    args = parser.parse_args()
    
    # 创建应用和管理器
    app = create_app()
    manager = DatabaseManager(app)
    
    # 执行命令
    try:
        if args.command == 'init':
            manager.init_database()
        elif args.command == 'seed':
            manager.seed_data(args.data_file)
        elif args.command == 'backup':
            manager.backup_database(args.backup_path)
        elif args.command == 'analyze':
            manager.analyze_database()
        elif args.command == 'clean':
            manager.clean_database(args.confirm)
        elif args.command == 'test-data':
            manager.import_test_data()
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    print("🚀 WakeUP 数据库管理工具")
    print("替代原有45个重复脚本的统一解决方案")
    print("-" * 50)
    main()