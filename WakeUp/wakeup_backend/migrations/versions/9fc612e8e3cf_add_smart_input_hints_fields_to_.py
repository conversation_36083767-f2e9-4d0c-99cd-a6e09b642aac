"""Add smart input hints fields to questions table

Revision ID: 9fc612e8e3cf
Revises: 2903f5611145
Create Date: 2025-08-18 19:15:29.198043

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9fc612e8e3cf'
down_revision = '2903f5611145'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('hints', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('templates', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('auto_complete', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('code_snippets', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('matching_hints', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('ordering_hints', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.drop_column('ordering_hints')
        batch_op.drop_column('matching_hints')
        batch_op.drop_column('code_snippets')
        batch_op.drop_column('auto_complete')
        batch_op.drop_column('templates')
        batch_op.drop_column('hints')

    # ### end Alembic commands ###
