"""Create courses table

Revision ID: 2903f5611145
Revises: 01fa64bbd527
Create Date: 2025-08-10 18:40:30.575378

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = '2903f5611145'
down_revision = '01fa64bbd527'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('courses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=False),
    sa.Column('english_name', sa.String(length=200), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('cover_image', sa.String(length=500), nullable=True),
    sa.Column('category_level2_id', sa.Integer(), nullable=False),
    sa.Column('difficulty_level', sa.String(length=20), nullable=True),
    sa.Column('duration_hours', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('display_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['category_level2_id'], ['category_level2.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('category_level5',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('english_name', sa.String(length=255), nullable=True),
    sa.Column('level4_id', sa.Integer(), nullable=True),
    sa.Column('tab_type', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['level4_id'], ['category_level4.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('category_level1', schema=None) as batch_op:
        batch_op.alter_column('english_name',
               existing_type=sa.TEXT(),
               type_=sa.String(length=255),
               existing_nullable=True)
        batch_op.drop_index('idx_category_level1_active')

    with op.batch_alter_table('category_level2', schema=None) as batch_op:
        batch_op.alter_column('english_name',
               existing_type=sa.TEXT(),
               type_=sa.String(length=255),
               existing_nullable=True)
        batch_op.drop_index('idx_category_level2_active')
        batch_op.drop_index('idx_category_level2_level1')

    with op.batch_alter_table('category_level3', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=128),
               existing_nullable=False)
        batch_op.alter_column('english_name',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=255),
               existing_nullable=True)
        batch_op.alter_column('level2_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.drop_index('idx_category_level3_active')
        batch_op.drop_index('idx_category_level3_level2_id')
        batch_op.drop_column('created_at')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('highlight')
        batch_op.drop_column('description')

    with op.batch_alter_table('category_level4', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=128),
               existing_nullable=False)
        batch_op.alter_column('english_name',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=255),
               existing_nullable=True)
        batch_op.alter_column('level3_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.drop_index('idx_category_level4_active')
        batch_op.drop_index('idx_category_level4_level3_id')
        batch_op.drop_column('created_at')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('highlight')
        batch_op.drop_column('description')

    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('question_text',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.Text(),
               existing_nullable=False)
        batch_op.alter_column('question_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=False,
               existing_server_default=sa.text("'multiple_choice'"))
        batch_op.alter_column('options',
               existing_type=sa.TEXT(),
               type_=sqlite.JSON(),
               existing_nullable=True)
        batch_op.alter_column('answer',
               existing_type=sa.TEXT(),
               type_=sqlite.JSON(),
               nullable=True)
        batch_op.alter_column('difficulty',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=20),
               existing_nullable=True,
               existing_server_default=sa.text('1'))
        batch_op.create_foreign_key(None, 'category_level4', ['category_level4_id'], ['id'])

    with op.batch_alter_table('region_categories', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)

    with op.batch_alter_table('regions', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('english_name',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
        batch_op.create_unique_constraint(None, ['code'])

    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.drop_index('idx_user_email')
        batch_op.drop_index('idx_user_phone')
        batch_op.drop_index('idx_user_token')
        batch_op.create_unique_constraint(None, ['username'])
        batch_op.create_unique_constraint(None, ['email'])
        batch_op.drop_column('role')
        batch_op.drop_column('last_login_at')

    with op.batch_alter_table('user_answers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('user_answer', sqlite.JSON(), nullable=True))
        batch_op.add_column(sa.Column('answer_time', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('attempt_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True))
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('question_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.drop_index('idx_user_answers_question')
        batch_op.drop_index('idx_user_answers_user')
        batch_op.create_foreign_key(None, 'user', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'questions', ['question_id'], ['id'])
        batch_op.drop_column('question_set_id')
        batch_op.drop_column('attempt_time')
        batch_op.drop_column('answer')

    with op.batch_alter_table('user_course', schema=None) as batch_op:
        batch_op.drop_index('idx_user_course_course')
        batch_op.drop_index('idx_user_course_user')
        batch_op.create_foreign_key(None, 'user', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'courses', ['course_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_course', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_index('idx_user_course_user', ['user_id'], unique=False)
        batch_op.create_index('idx_user_course_course', ['course_id'], unique=False)

    with op.batch_alter_table('user_answers', schema=None) as batch_op:
        batch_op.add_column(sa.Column('answer', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('attempt_time', sa.DATETIME(), nullable=True))
        batch_op.add_column(sa.Column('question_set_id', sa.INTEGER(), nullable=True))
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_index('idx_user_answers_user', ['user_id'], unique=False)
        batch_op.create_index('idx_user_answers_question', ['question_id'], unique=False)
        batch_op.alter_column('question_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.drop_column('updated_at')
        batch_op.drop_column('created_at')
        batch_op.drop_column('attempt_count')
        batch_op.drop_column('answer_time')
        batch_op.drop_column('user_answer')

    with op.batch_alter_table('user', schema=None) as batch_op:
        batch_op.add_column(sa.Column('last_login_at', sa.DATETIME(), nullable=True))
        batch_op.add_column(sa.Column('role', sa.VARCHAR(length=20), server_default=sa.text("'user'"), nullable=True))
        batch_op.drop_constraint(None, type_='unique')
        batch_op.drop_constraint(None, type_='unique')
        batch_op.create_index('idx_user_token', ['token'], unique=False)
        batch_op.create_index('idx_user_phone', ['phone'], unique=False)
        batch_op.create_index('idx_user_email', ['email'], unique=False)

    with op.batch_alter_table('regions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.alter_column('english_name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('region_categories', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('questions', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.alter_column('difficulty',
               existing_type=sa.String(length=20),
               type_=sa.INTEGER(),
               existing_nullable=True,
               existing_server_default=sa.text('1'))
        batch_op.alter_column('answer',
               existing_type=sqlite.JSON(),
               type_=sa.TEXT(),
               nullable=False)
        batch_op.alter_column('options',
               existing_type=sqlite.JSON(),
               type_=sa.TEXT(),
               existing_nullable=True)
        batch_op.alter_column('question_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True,
               existing_server_default=sa.text("'multiple_choice'"))
        batch_op.alter_column('question_text',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(length=500),
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('category_level4', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('highlight', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True))
        batch_op.create_index('idx_category_level4_level3_id', ['level3_id'], unique=False)
        batch_op.create_index('idx_category_level4_active', ['is_active'], unique=False)
        batch_op.alter_column('level3_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('english_name',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
        batch_op.alter_column('name',
               existing_type=sa.String(length=128),
               type_=sa.VARCHAR(length=100),
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('category_level3', schema=None) as batch_op:
        batch_op.add_column(sa.Column('description', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('highlight', sa.TEXT(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True))
        batch_op.create_index('idx_category_level3_level2_id', ['level2_id'], unique=False)
        batch_op.create_index('idx_category_level3_active', ['is_active'], unique=False)
        batch_op.alter_column('level2_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('english_name',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
        batch_op.alter_column('name',
               existing_type=sa.String(length=128),
               type_=sa.VARCHAR(length=100),
               existing_nullable=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('category_level2', schema=None) as batch_op:
        batch_op.create_index('idx_category_level2_level1', ['level1_id'], unique=False)
        batch_op.create_index('idx_category_level2_active', ['is_active'], unique=False)
        batch_op.alter_column('english_name',
               existing_type=sa.String(length=255),
               type_=sa.TEXT(),
               existing_nullable=True)

    with op.batch_alter_table('category_level1', schema=None) as batch_op:
        batch_op.create_index('idx_category_level1_active', ['is_active'], unique=False)
        batch_op.alter_column('english_name',
               existing_type=sa.String(length=255),
               type_=sa.TEXT(),
               existing_nullable=True)

    op.drop_table('category_level5')
    op.drop_table('courses')
    # ### end Alembic commands ###
