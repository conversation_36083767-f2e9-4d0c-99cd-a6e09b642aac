#!/usr/bin/env python3
"""
数据库迁移脚本：将现有用户表迁移为纯OAuth登录模式
执行命令：python migrate_to_pure_oauth.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app, db
from models.user import User
from datetime import datetime

def migrate_to_pure_oauth():
    """迁移现有数据库到纯OAuth模式"""
    
    with app.app_context():
        print("🚀 开始迁移数据库到纯OAuth登录模式...")
        
        try:
            # 1. 更新表结构 - 修改phone字段为可空
            db.engine.execute('ALTER TABLE user MODIFY COLUMN phone VARCHAR(20) NULL;')
            print("✅ 已更新phone字段为可空")
            
            # 2. 更新表结构 - 修改oauth_provider和oauth_open_id为必填
            db.engine.execute('ALTER TABLE user MODIFY COLUMN oauth_provider VARCHAR(20) NOT NULL;')
            db.engine.execute('ALTER TABLE user MODIFY COLUMN oauth_open_id VARCHAR(100) NOT NULL;')
            print("✅ 已更新OAuth字段约束")
            
            # 3. 为现有用户添加复合唯一索引
            try:
                db.engine.execute('CREATE UNIQUE INDEX idx_oauth_provider_open_id ON user(oauth_provider, oauth_open_id);')
                print("✅ 已创建OAuth复合唯一索引")
            except Exception as e:
                if "Duplicate entry" in str(e) or "already exists" in str(e):
                    print("⚠️  OAuth复合唯一索引已存在，跳过")
                else:
                    raise e
            
            # 4. 处理现有传统注册用户 - 为他们分配虚拟OAuth信息
            legacy_users = User.query.filter(
                (User.oauth_provider.is_(None)) | (User.oauth_open_id.is_(None))
            ).all()
            
            if legacy_users:
                print(f"📋 发现 {len(legacy_users)} 个传统注册用户，正在迁移...")
                
                for user in legacy_users:
                    # 为传统用户分配"legacy"提供商
                    user.oauth_provider = 'legacy'
                    user.oauth_open_id = f'legacy_user_{user.id}'
                    
                    # 确保有昵称
                    if not user.nickname:
                        user.nickname = user.username or f'用户{user.id}'
                    
                    # 更新时间戳
                    user.updated_at = datetime.utcnow()
                    
                print(f"✅ 已迁移 {len(legacy_users)} 个传统用户为legacy模式")
            
            # 5. 提交所有更改
            db.session.commit()
            print("✅ 数据库迁移完成！")
            
            # 6. 验证迁移结果
            total_users = User.query.count()
            oauth_users = User.query.filter(User.oauth_provider.isnot(None)).count()
            print(f"📊 迁移结果：总用户数 {total_users}，OAuth用户数 {oauth_users}")
            
            if total_users == oauth_users:
                print("🎉 所有用户都已成功迁移到OAuth模式！")
            else:
                print(f"⚠️  仍有 {total_users - oauth_users} 个用户未完全迁移")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 迁移失败: {e}")
            raise e

def rollback_migration():
    """回滚迁移（如果需要）"""
    with app.app_context():
        print("🔄 开始回滚OAuth迁移...")
        
        try:
            # 回滚phone字段约束
            db.engine.execute('ALTER TABLE user MODIFY COLUMN phone VARCHAR(20) NOT NULL;')
            
            # 删除OAuth复合索引
            try:
                db.engine.execute('DROP INDEX idx_oauth_provider_open_id ON user;')
            except:
                pass
            
            print("✅ 迁移已回滚")
            
        except Exception as e:
            print(f"❌ 回滚失败: {e}")
            raise e

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库OAuth迁移工具')
    parser.add_argument('action', choices=['migrate', 'rollback'], 
                       help='执行迁移(migrate)或回滚(rollback)')
    
    args = parser.parse_args()
    
    if args.action == 'migrate':
        migrate_to_pure_oauth()
    elif args.action == 'rollback':
        rollback_migration()
    
    print("🏁 操作完成！")