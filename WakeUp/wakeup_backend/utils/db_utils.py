"""
数据库工具函数库
提供常用的数据库操作工具函数，避免在多个脚本中重复实现
"""

import json
import csv
from typing import List, Dict, Any, Optional
from sqlalchemy.exc import IntegrityError
from models import db

class DatabaseUtils:
    """数据库工具类"""
    
    @staticmethod
    def bulk_insert(model_class, data_list: List[Dict], batch_size: int = 1000):
        """批量插入数据"""
        total_inserted = 0
        total_failed = 0
        
        for i in range(0, len(data_list), batch_size):
            batch = data_list[i:i + batch_size]
            try:
                # 使用批量插入提高性能
                db.session.bulk_insert_mappings(model_class, batch)
                db.session.commit()
                total_inserted += len(batch)
                print(f"✅ 批量插入 {len(batch)} 条记录到 {model_class.__tablename__}")
            except IntegrityError as e:
                db.session.rollback()
                # 如果批量插入失败，逐条插入以识别问题记录
                single_inserted, single_failed = DatabaseUtils._single_insert_batch(
                    model_class, batch
                )
                total_inserted += single_inserted
                total_failed += single_failed
                print(f"⚠️  批量插入失败，逐条处理: 成功 {single_inserted}, 失败 {single_failed}")
            except Exception as e:
                db.session.rollback()
                print(f"❌ 批量插入失败: {e}")
                total_failed += len(batch)
        
        return {
            'inserted': total_inserted,
            'failed': total_failed,
            'total': len(data_list)
        }
    
    @staticmethod
    def _single_insert_batch(model_class, batch: List[Dict]):
        """逐条插入批次数据"""
        inserted = 0
        failed = 0
        
        for record in batch:
            try:
                instance = model_class(**record)
                db.session.add(instance)
                db.session.commit()
                inserted += 1
            except Exception as e:
                db.session.rollback()
                failed += 1
                print(f"❌ 插入失败: {record.get('name', record.get('id', 'unknown'))}, 错误: {e}")
        
        return inserted, failed
    
    @staticmethod
    def export_to_json(model_class, output_file: str, limit: Optional[int] = None):
        """导出表数据到JSON文件"""
        try:
            query = model_class.query
            if limit:
                query = query.limit(limit)
            
            records = query.all()
            data = []
            
            for record in records:
                # 假设模型有to_dict方法
                if hasattr(record, 'to_dict'):
                    data.append(record.to_dict())
                else:
                    # 使用sqlalchemy inspect获取列
                    record_dict = {}
                    for column in model_class.__table__.columns:
                        record_dict[column.name] = getattr(record, column.name)
                    data.append(record_dict)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 导出 {len(data)} 条记录到 {output_file}")
            return len(data)
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return 0
    
    @staticmethod
    def export_to_csv(model_class, output_file: str, limit: Optional[int] = None):
        """导出表数据到CSV文件"""
        try:
            query = model_class.query
            if limit:
                query = query.limit(limit)
            
            records = query.all()
            if not records:
                print("⚠️  没有数据可导出")
                return 0
            
            # 获取列名
            columns = [column.name for column in model_class.__table__.columns]
            
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(columns)  # 写入标题行
                
                for record in records:
                    row = [getattr(record, col) for col in columns]
                    writer.writerow(row)
            
            print(f"✅ 导出 {len(records)} 条记录到 {output_file}")
            return len(records)
        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return 0
    
    @staticmethod
    def import_from_csv(model_class, csv_file: str, skip_header: bool = True):
        """从CSV文件导入数据"""
        try:
            data_list = []
            columns = [column.name for column in model_class.__table__.columns]
            
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                
                if skip_header:
                    next(reader)  # 跳过标题行
                
                for row in reader:
                    if len(row) == len(columns):
                        record = dict(zip(columns, row))
                        # 处理空值
                        for key, value in record.items():
                            if value == '' or value == 'NULL':
                                record[key] = None
                        data_list.append(record)
                    else:
                        print(f"⚠️  跳过不匹配的行: {row}")
            
            if data_list:
                result = DatabaseUtils.bulk_insert(model_class, data_list)
                print(f"📥 从 {csv_file} 导入完成: {result}")
                return result
            else:
                print("⚠️  没有有效数据可导入")
                return {'inserted': 0, 'failed': 0, 'total': 0}
        except Exception as e:
            print(f"❌ 导入失败: {e}")
            return {'inserted': 0, 'failed': 0, 'total': 0}
    
    @staticmethod
    def clean_duplicate_records(model_class, unique_fields: List[str]):
        """清理重复记录"""
        print(f"🧹 正在清理 {model_class.__tablename__} 表的重复记录...")
        
        # 构建去重查询
        # 这是一个简化实现，实际可能需要更复杂的逻辑
        try:
            # 查找重复记录
            duplicates = db.session.query(model_class).all()
            seen = set()
            to_delete = []
            
            for record in duplicates:
                # 创建唯一标识
                key = tuple(getattr(record, field) for field in unique_fields)
                if key in seen:
                    to_delete.append(record)
                else:
                    seen.add(key)
            
            # 删除重复记录
            for record in to_delete:
                db.session.delete(record)
            
            db.session.commit()
            print(f"✅ 清理了 {len(to_delete)} 条重复记录")
            return len(to_delete)
        except Exception as e:
            db.session.rollback()
            print(f"❌ 清理重复记录失败: {e}")
            return 0
    
    @staticmethod
    def validate_data_integrity():
        """验证数据完整性"""
        print("🔍 验证数据完整性...")
        issues = []
        
        try:
            # 检查外键完整性
            # 这里添加具体的验证逻辑
            # 例如：检查course_id是否存在对应的course记录
            
            print("✅ 数据完整性验证完成")
            if issues:
                print("⚠️  发现以下问题:")
                for issue in issues:
                    print(f"  - {issue}")
            
            return issues
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return [str(e)]
    
    @staticmethod
    def update_statistics():
        """更新统计信息"""
        print("📊 更新数据库统计信息...")
        try:
            # SQLite的ANALYZE命令
            db.session.execute("ANALYZE")
            db.session.commit()
            print("✅ 统计信息更新完成")
        except Exception as e:
            print(f"❌ 更新统计信息失败: {e}")

class DataTransformer:
    """数据转换工具类"""
    
    @staticmethod
    def normalize_category_names(data_list: List[Dict]) -> List[Dict]:
        """标准化分类名称"""
        for item in data_list:
            if 'name' in item:
                # 移除多余空格
                item['name'] = item['name'].strip()
                # 标准化大小写
                # 可以添加更多标准化逻辑
        return data_list
    
    @staticmethod
    def generate_english_names(data_list: List[Dict]) -> List[Dict]:
        """为中文名称生成英文名称（简化实现）"""
        # 这里可以集成翻译API或使用映射表
        name_mapping = {
            '计算机科学': 'Computer Science',
            '数据科学': 'Data Science',
            'Web开发': 'Web Development',
            'Python基础': 'Python Basics',
            # 添加更多映射
        }
        
        for item in data_list:
            if 'name' in item and 'english_name' not in item:
                chinese_name = item['name']
                item['english_name'] = name_mapping.get(chinese_name, chinese_name)
        
        return data_list
    
    @staticmethod
    def validate_required_fields(data_list: List[Dict], required_fields: List[str]) -> List[Dict]:
        """验证必需字段"""
        valid_data = []
        
        for item in data_list:
            is_valid = True
            for field in required_fields:
                if field not in item or item[field] is None or item[field] == '':
                    print(f"⚠️  记录缺少必需字段 {field}: {item}")
                    is_valid = False
                    break
            
            if is_valid:
                valid_data.append(item)
        
        print(f"✅ 验证完成: {len(valid_data)}/{len(data_list)} 记录有效")
        return valid_data