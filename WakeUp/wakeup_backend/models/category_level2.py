from . import db
from sqlalchemy.orm import relationship

class CategoryLevel2(db.Model):
    __tablename__ = 'category_level2'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    level1_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('category_level1.id'))
    name = db.Column(db.String(100), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)

    # 新增字段
    description = db.Column(db.String(255), default="")
    highlight = db.Column(db.String(255), default="")
    english_name = db.Column(db.String(255))  # 添加英文名称字段

    level1 = relationship("CategoryLevel1", backref="level2_categories")

    def to_dict(self):
        return {
            "id": self.id,
            "level1_id": self.level1_id,
            "name": self.name,
            "is_active": self.is_active,
            "description": self.description,
            "highlight": self.highlight,
            "english_name": self.english_name
        }
