from . import db
from datetime import datetime

class RegionCategory(db.Model):
    """地区分类关联模型"""
    __tablename__ = 'region_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    region_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('regions.id'), nullable=False)
    category_level = db.Column(db.Integer, nullable=False)  # 分类级别：1,2,3,4,5
    category_id = db.Column(db.Integer, nullable=False)  # 分类ID
    display_order = db.Column(db.Integer, default=0)  # 显示顺序
    is_active = db.Column(db.<PERSON><PERSON>, default=True)  # 是否启用
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 外键关系
    region = db.relationship('Region', backref='region_categories')
    
    def to_dict(self):
        return {
            'id': self.id,
            'region_id': self.region_id,
            'category_level': self.category_level,
            'category_id': self.category_id,
            'display_order': self.display_order,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        } 