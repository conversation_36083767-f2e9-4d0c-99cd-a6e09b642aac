from . import db
from .category_level3 import CategoryLevel3
from sqlalchemy.orm import relationship

class CategoryLevel4(db.Model):
    __tablename__ = 'category_level4'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    english_name = db.Column(db.String(255))  # 添加英文名称字段

    level3_id = db.Column(db.In<PERSON>ger, db.ForeignKey('category_level3.id'))
    level3 = db.relationship("CategoryLevel3", backref="level4_list")
    
    # 关联五级分类通过level5_categories反向引用提供
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "level3_id": self.level3_id,
            "is_active": self.is_active,
            "english_name": self.english_name
        }

    # 为了保持API兼容性，level5_list需要包含直接关联的题库和通过五级分类关联的题库
    @property
    def level5_list(self):
        result = []
        # 获取直接关联的题库(遗留方式)
        if hasattr(self, 'question_sets'):
            result.extend(self.question_sets)
        
        # 获取通过五级分类关联的内容(新方式)
        if hasattr(self, 'level5_categories'):
            # 暂时先获取所有五级分类，未来可能需要根据应用场景过滤
            result.extend(self.level5_categories)
        
        return result
    
    # 获取该四级分类下的所有五级分类
    @property
    def get_level5_categories(self):
        if hasattr(self, 'level5_categories'):
            return self.level5_categories
        return []
    
    # 获取该四级分类下特定类型的六级内容
    def get_level6_items(self, content_type='quiz'):
        
        result = []
        # 1. 通过五级分类获取
        if hasattr(self, 'level5_categories'):
            for cat5 in self.level5_categories:
                if cat5.tab_type == content_type:
                    result.extend(cat5.get_level6_items())
        
        # 2. 对于quiz类型，还要考虑直接关联的题库(遗留方式)
        if content_type == 'quiz' and hasattr(self, 'question_sets'):
            # 只获取没有关联到五级分类的题库
            direct_question_sets = [qs for qs in self.question_sets if not qs.category_level5_id]
            result.extend(direct_question_sets)
        
        return result
