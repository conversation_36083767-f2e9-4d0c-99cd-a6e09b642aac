from . import db
from datetime import datetime

class UserCourse(db.Model):
    """用户课程关联模型"""
    __tablename__ = 'user_course'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('user.id'), nullable=False)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'), nullable=False)
    
    # 基本字段（已存在）
    course_name = db.Column(db.String(100))
    description = db.Column(db.String(255))
    cover_image = db.Column(db.String(255))
    english_name = db.Column(db.Text)
    
    # 新增的学习管理字段
    status = db.Column(db.String(50), default='not_started')
    progress_percentage = db.Column(db.Integer, default=0)
    study_hours = db.Column(db.Integer, default=0)
    correct_answers = db.Column(db.Integer, default=0)
    total_answers = db.Column(db.Integer, default=0)
    last_studied_at = db.Column(db.DateTime)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    
    # 外键关系
    user = db.relationship('User', backref='user_courses')
    course = db.relationship('Course', backref='user_enrollments')
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'course_id': self.course_id,
            'course_name': self.course_name,
            'description': self.description,
            'cover_image': self.cover_image,
            'english_name': self.english_name,
            'status': self.status,
            'progress_percentage': self.progress_percentage,
            'study_hours': self.study_hours,
            'correct_answers': self.correct_answers,
            'total_answers': self.total_answers,
            'last_studied_at': self.last_studied_at.isoformat() if self.last_studied_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
        }
