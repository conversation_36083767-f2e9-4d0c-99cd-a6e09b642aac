from . import db
from datetime import datetime

class Course(db.Model):
    """课程模型"""
    __tablename__ = 'courses'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)  # 课程名称
    english_name = db.Column(db.String(200), nullable=True)  # 英文名称
    description = db.Column(db.Text, nullable=True)  # 课程描述
    cover_image = db.Column(db.String(500), nullable=True)  # 封面图片
    category_level2_id = db.Column(db.Integer, db.<PERSON>Key('category_level2.id'), nullable=False)  # 关联二级分类
    difficulty_level = db.Column(db.String(20), default='beginner')  # 难度级别：beginner, intermediate, advanced
    duration_hours = db.Column(db.Integer, default=0)  # 预计学习时长（小时）
    is_active = db.Column(db.<PERSON>, default=True)  # 是否启用
    display_order = db.Column(db.Integer, default=0)  # 显示顺序
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 外键关系
    category_level2 = db.relationship('CategoryLevel2', backref='courses')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'english_name': self.english_name,
            'description': self.description,
            'cover_image': self.cover_image,
            'category_level2_id': self.category_level2_id,
            'difficulty_level': self.difficulty_level,
            'duration_hours': self.duration_hours,
            'is_active': self.is_active,
            'display_order': self.display_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        } 