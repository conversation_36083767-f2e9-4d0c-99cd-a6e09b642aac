from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

# 导入所有模型，确保在数据库初始化时被注册
from .user import User
from .region import Region
from .region_category import RegionCategory
from .category_level1 import CategoryLevel1
from .category_level2 import CategoryLevel2
from .category_level3 import CategoryLevel3
from .category_level4 import CategoryLevel4
from .category_level5 import CategoryLevel5
from .course import Course
from .user_course import UserCourse
from .question import Question
from .user_answer import UserAnswer
