from flask import Blueprint, request, jsonify
from models import db
from models.user_course import UserCourse
from models.user import User
from utils.auth import token_required

user_course_bp = Blueprint('user_course', __name__)

@user_course_bp.route('/api/user_course/join', methods=['POST'])
@token_required
def join_course(user):
    data = request.get_json()
    user_id = data.get('user_id')
    
    # 验证请求中的user_id与令牌中的用户匹配
    if int(user_id) != user.id:
        return jsonify({'message': '授权用户与请求用户不匹配'}), 403
    
    course_id = data.get('course_id')
    course_name = data.get('course_name')
    description = data.get('description', '')
    cover_image = data.get('cover_image', '')
    english_name = data.get('english_name', '')

    existing = UserCourse.query.filter_by(user_id=user_id, course_id=course_id).first()
    if existing:
        return jsonify({'message': '课程已加入'}), 200

    new_record = UserCourse(
        user_id=user_id,
        course_id=course_id,
        course_name=course_name,
        description=description,
        cover_image=cover_image,
        english_name=english_name
    )
    db.session.add(new_record)
    db.session.commit()
    return jsonify({'message': '课程加入成功', 'status': 'success'}), 201

@user_course_bp.route('/api/user_course/list/<int:user_id>', methods=['GET'])
@token_required
def get_user_courses(user_id, user):
    # 验证请求中的user_id与令牌中的用户匹配
    if user_id != user.id:
        return jsonify({'message': '授权用户与请求用户不匹配'}), 403
    
    records = UserCourse.query.filter_by(user_id=user_id).all()
    result = []
    for r in records:
        result.append({
            'course_id': r.course_id,
            'course_name': r.course_name,
            'description': r.description,
            'cover_image': r.cover_image,
            'english_name': r.english_name
        })
    return jsonify(result)

@user_course_bp.route('/api/user_course/leave', methods=['POST'])
@token_required
def leave_course(user):
    data = request.get_json()
    user_id = data.get('user_id')
    
    # 验证请求中的user_id与令牌中的用户匹配
    if int(user_id) != user.id:
        return jsonify({'message': '授权用户与请求用户不匹配'}), 403
    
    course_id = data.get('course_id')

    record = UserCourse.query.filter_by(user_id=user_id, course_id=course_id).first()
    if not record:
        return jsonify({'message': '未找到此课程'}), 404

    db.session.delete(record)
    db.session.commit()
    return jsonify({'message': '成功退出课程', 'status': 'success'}), 200
