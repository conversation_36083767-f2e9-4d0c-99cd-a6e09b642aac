from flask import Blueprint, request, jsonify
from models.region import Region
from models.region_category import RegionCategory
from models.category_level1 import CategoryLevel1
from models.category_level2 import CategoryLevel2
from models import db
from sqlalchemy import and_

region_bp = Blueprint('region', __name__)

@region_bp.route('/api/regions', methods=['GET'])
def get_regions():
    """获取所有活跃的地区列表"""
    try:
        regions = Region.query.filter_by(is_active=True).order_by(Region.name).all()
        return jsonify({
            'success': True,
            'data': [region.to_dict() for region in regions]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取地区列表失败: {str(e)}'
        }), 500

@region_bp.route('/api/regions/<region_code>/categories/level1', methods=['GET'])
def get_region_level1_categories(region_code):
    """获取指定地区的一级分类"""
    try:
        # 获取地区信息
        region = Region.query.filter_by(code=region_code, is_active=True).first()
        if not region:
            return jsonify({
                'success': False,
                'message': f'地区 {region_code} 不存在或已禁用'
            }), 404
        
        # 获取该地区关联的一级分类
        region_categories = db.session.query(RegionCategory, CategoryLevel1).join(
            CategoryLevel1, 
            and_(
                RegionCategory.category_id == CategoryLevel1.id,
                RegionCategory.category_level == 1
            )
        ).filter(
            RegionCategory.region_id == region.id,
            RegionCategory.is_active == True,
            CategoryLevel1.is_active == True
        ).order_by(RegionCategory.display_order, CategoryLevel1.name).all()
        
        categories = []
        for region_cat, category in region_categories:
            cat_dict = category.to_dict()
            cat_dict['display_order'] = region_cat.display_order
            categories.append(cat_dict)
        
        return jsonify({
            'success': True,
            'data': categories,
            'region': region.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取地区分类失败: {str(e)}'
        }), 500

@region_bp.route('/api/regions/<region_code>/categories/level2', methods=['GET'])
def get_region_level2_categories(region_code):
    """获取指定地区的二级分类"""
    try:
        level1_id = request.args.get('level1_id', type=int)
        
        # 获取地区信息
        region = Region.query.filter_by(code=region_code, is_active=True).first()
        if not region:
            return jsonify({
                'success': False,
                'message': f'地区 {region_code} 不存在或已禁用'
            }), 404
        
        # 构建查询条件
        query_conditions = [
            RegionCategory.region_id == region.id,
            RegionCategory.category_level == 2,
            RegionCategory.is_active == True,
            CategoryLevel2.is_active == True
        ]
        
        if level1_id:
            query_conditions.append(CategoryLevel2.level1_id == level1_id)
        
        # 获取该地区关联的二级分类
        region_categories = db.session.query(RegionCategory, CategoryLevel2).join(
            CategoryLevel2,
            and_(
                RegionCategory.category_id == CategoryLevel2.id,
                RegionCategory.category_level == 2
            )
        ).filter(
            and_(*query_conditions)
        ).order_by(RegionCategory.display_order, CategoryLevel2.name).all()
        
        categories = []
        for region_cat, category in region_categories:
            cat_dict = category.to_dict()
            cat_dict['display_order'] = region_cat.display_order
            categories.append(cat_dict)
        
        return jsonify({
            'success': True,
            'data': categories,
            'region': region.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取地区分类失败: {str(e)}'
        }), 500

@region_bp.route('/api/regions', methods=['POST'])
def create_region():
    """创建新地区"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['code', 'name', 'english_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查地区代码是否已存在
        existing_region = Region.query.filter_by(code=data['code']).first()
        if existing_region:
            return jsonify({
                'success': False,
                'message': f'地区代码 {data["code"]} 已存在'
            }), 400
        
        # 创建新地区
        region = Region(
            code=data['code'].upper(),
            name=data['name'],
            english_name=data['english_name'],
            is_active=data.get('is_active', True)
        )
        
        db.session.add(region)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '地区创建成功',
            'data': region.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建地区失败: {str(e)}'
        }), 500

@region_bp.route('/api/regions/<int:region_id>/categories', methods=['POST'])
def add_region_category_association(region_id):
    """为地区添加分类关联"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['category_level', 'category_id']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 检查地区是否存在
        region = Region.query.get(region_id)
        if not region:
            return jsonify({
                'success': False,
                'message': f'地区 {region_id} 不存在'
            }), 404
        
        # 检查是否已存在相同的关联
        existing_association = RegionCategory.query.filter_by(
            region_id=region_id,
            category_level=data['category_level'],
            category_id=data['category_id']
        ).first()
        
        if existing_association:
            return jsonify({
                'success': False,
                'message': '该分类关联已存在'
            }), 400
        
        # 创建新的地区分类关联
        region_category = RegionCategory(
            region_id=region_id,
            category_level=data['category_level'],
            category_id=data['category_id'],
            display_order=data.get('display_order', 0),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(region_category)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '地区分类关联创建成功',
            'data': region_category.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建地区分类关联失败: {str(e)}'
        }), 500 