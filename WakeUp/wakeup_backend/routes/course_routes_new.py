from flask import Blueprint, request, jsonify
from models.course import Course
from models.user_course import UserCourse
from models.category_level2 import CategoryLevel2
from models.user import User
from models.question import Question
from models import db
from datetime import datetime
from sqlalchemy import and_, or_

course_new_bp = Blueprint('course_new', __name__)

@course_new_bp.route('/api/courses', methods=['GET'])
def get_courses():
    """获取课程列表"""
    try:
        category_level2_id = request.args.get('category_level2_id', type=int)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 构建查询
        query = Course.query.filter_by(is_active=True)
        
        if category_level2_id:
            query = query.filter_by(category_level2_id=category_level2_id)
        
        # 分页
        courses = query.order_by(Course.display_order, Course.name).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        course_list = []
        for course in courses.items:
            course_dict = course.to_dict()
            # 添加分类信息
            if course.category_level2:
                course_dict['category'] = course.category_level2.to_dict()
            course_list.append(course_dict)
        
        return jsonify({
            'success': True,
            'data': course_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': courses.total,
                'pages': courses.pages
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取课程列表失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/courses/<int:course_id>', methods=['GET'])
def get_course_detail(course_id):
    """获取课程详情"""
    try:
        course = Course.query.get(course_id)
        if not course:
            return jsonify({
                'success': False,
                'message': '课程不存在'
            }), 404
        
        course_dict = course.to_dict()
        
        # 添加分类信息
        if course.category_level2:
            course_dict['category'] = course.category_level2.to_dict()
        
        # 添加统计信息
        total_enrollments = UserCourse.query.filter_by(course_id=course_id).count()
        course_dict['total_enrollments'] = total_enrollments
        
        return jsonify({
            'success': True,
            'data': course_dict
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取课程详情失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/courses', methods=['POST'])
def create_course():
    """创建新课程"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'category_level2_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }), 400
        
        # 验证二级分类是否存在
        category = CategoryLevel2.query.get(data['category_level2_id'])
        if not category:
            return jsonify({
                'success': False,
                'message': '指定的二级分类不存在'
            }), 400
        
        # 创建新课程
        course = Course(
            name=data['name'],
            english_name=data.get('english_name'),
            description=data.get('description'),
            cover_image=data.get('cover_image'),
            category_level2_id=data['category_level2_id'],
            difficulty_level=data.get('difficulty_level', 'beginner'),
            duration_hours=data.get('duration_hours', 0),
            display_order=data.get('display_order', 0),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(course)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '课程创建成功',
            'data': course.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'创建课程失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/courses/<int:course_id>', methods=['PUT'])
def update_course(course_id):
    """更新课程信息"""
    try:
        course = Course.query.get(course_id)
        if not course:
            return jsonify({
                'success': False,
                'message': '课程不存在'
            }), 404
        
        data = request.get_json()
        
        # 更新字段
        if 'name' in data:
            course.name = data['name']
        if 'english_name' in data:
            course.english_name = data['english_name']
        if 'description' in data:
            course.description = data['description']
        if 'cover_image' in data:
            course.cover_image = data['cover_image']
        if 'difficulty_level' in data:
            course.difficulty_level = data['difficulty_level']
        if 'duration_hours' in data:
            course.duration_hours = data['duration_hours']
        if 'display_order' in data:
            course.display_order = data['display_order']
        if 'is_active' in data:
            course.is_active = data['is_active']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '课程更新成功',
            'data': course.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新课程失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/courses/<int:course_id>', methods=['DELETE'])
def delete_course(course_id):
    """删除课程（软删除）"""
    try:
        course = Course.query.get(course_id)
        if not course:
            return jsonify({
                'success': False,
                'message': '课程不存在'
            }), 404
        
        # 软删除：设置为不活跃
        course.is_active = False
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '课程删除成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除课程失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/courses/<int:course_id>/enroll', methods=['POST'])
def enroll_course(course_id):
    """用户加入课程"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({
                'success': False,
                'message': '缺少用户ID'
            }), 400
        
        # 验证课程是否存在
        course = Course.query.get(course_id)
        if not course or not course.is_active:
            return jsonify({
                'success': False,
                'message': '课程不存在或已禁用'
            }), 404
        
        # 验证用户是否存在
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        # 检查是否已经加入过
        existing_enrollment = UserCourse.query.filter_by(
            user_id=user_id,
            course_id=course_id
        ).first()
        
        if existing_enrollment:
            return jsonify({
                'success': False,
                'message': '已经加入过该课程'
            }), 400
        
        # 创建用户课程关联
        user_course = UserCourse(
            user_id=user_id,
            course_id=course_id,
            course_name=course.name,
            description=course.description,
            status='enrolled',
            started_at=datetime.utcnow()
        )
        
        db.session.add(user_course)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '加入课程成功',
            'data': user_course.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'加入课程失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/users/<int:user_id>/courses', methods=['GET'])
def get_user_courses(user_id):
    """获取用户的课程列表"""
    try:
        # 验证用户是否存在
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在'
            }), 404
        
        status = request.args.get('status')  # enrolled, in_progress, completed, paused
        
        # 构建查询
        query = db.session.query(UserCourse, Course).join(
            Course, UserCourse.course_id == Course.id
        ).filter(
            UserCourse.user_id == user_id,
            Course.is_active == True
        )
        
        if status:
            query = query.filter(UserCourse.status == status)
        
        # 获取用户课程
        user_courses = query.order_by(UserCourse.started_at.desc()).all()
        
        course_list = []
        for user_course, course in user_courses:
            course_dict = course.to_dict()
            course_dict['enrollment'] = user_course.to_dict()
            course_list.append(course_dict)
        
        return jsonify({
            'success': True,
            'data': course_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取用户课程失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/user-courses/<int:user_course_id>', methods=['PUT'])
def update_user_course_progress(user_course_id):
    """更新用户课程学习进度"""
    try:
        user_course = UserCourse.query.get(user_course_id)
        if not user_course:
            return jsonify({
                'success': False,
                'message': '用户课程记录不存在'
            }), 404
        
        data = request.get_json()
        
        # 更新学习进度
        if 'progress_percentage' in data:
            user_course.progress_percentage = max(0, min(100, data['progress_percentage']))
        
        if 'status' in data:
            user_course.status = data['status']
        
        if 'study_hours' in data:
            user_course.study_hours += data['study_hours']
        
        if 'total_answers' in data:
            user_course.total_answers += data['total_answers']
        
        if 'correct_answers' in data:
            user_course.correct_answers += data['correct_answers']
        
        # 更新最后学习时间
        user_course.last_studied_at = datetime.utcnow()
        
        # 如果进度达到100%，标记为完成
        if user_course.progress_percentage >= 100 and user_course.status != 'completed':
            user_course.status = 'completed'
            user_course.completed_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '学习进度更新成功',
            'data': user_course.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新学习进度失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/courses/<int:course_id>/questions', methods=['GET'])
def get_course_questions(course_id):
    """获取课程关联的题库"""
    try:
        # 验证课程是否存在
        course = Course.query.get(course_id)
        if not course:
            return jsonify({
                'success': False,
                'message': '课程不存在'
            }), 404
        
        # 简化版本：直接获取所有活跃的题目
        questions = Question.query.filter_by(is_active=True).all()
        
        question_list = []
        for question in questions:
            question_list.append(question.to_dict())
        
        return jsonify({
            'success': True,
            'data': question_list,
            'course': course.to_dict(),
            'total_questions': len(question_list)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取课程题库失败: {str(e)}'
        }), 500

@course_new_bp.route('/api/course_tabs', methods=['GET'])
def get_course_tabs():
    """获取课程分类标签"""
    try:
        course_id = request.args.get('course_id', type=int)
        if not course_id:
            return jsonify({
                'success': False,
                'message': '缺少课程ID参数'
            }), 400
        
        # 验证课程是否存在
        course = Course.query.get(course_id)
        if not course:
            return jsonify({
                'success': False,
                'message': '课程不存在'
            }), 404
        
        # 根据课程的二级分类获取相关的标签
        from models.category_level3 import CategoryLevel3
        from models.category_level4 import CategoryLevel4
        from models.category_level5 import CategoryLevel5
        
        # 获取三级分类
        level3_categories = CategoryLevel3.query.filter_by(
            level2_id=course.category_level2_id,
            is_active=True
        ).all()
        
        tabs = []
        
        # 添加默认的"全部题目"标签
        tabs.append({
            'id': 0,
            'name': '全部题目',
            'english_name': 'All Questions',
            'description': '所有题目',
            'level4_id': course_id,
            'is_active': True,
            'tab_type': 'quiz'
        })
        
        # 为每个三级分类创建标签
        for level3 in level3_categories:
            # 获取该三级分类下的四级分类
            level4_categories = CategoryLevel4.query.filter_by(
                level3_id=level3.id,
                is_active=True
            ).all()
            
            for level4 in level4_categories:
                # 检查该四级分类下是否有题目
                question_count = Question.query.filter_by(
                    category_level4_id=level4.id,
                    is_active=True
                ).count()
                
                if question_count > 0:
                    tabs.append({
                        'id': level4.id,
                        'name': level4.name,
                        'english_name': level4.english_name or level4.name,
                        'description': f'{level3.name} - {level4.name}',
                        'level4_id': level4.id,
                        'is_active': True,
                        'tab_type': 'quiz'
                    })
        
        # 添加其他功能标签
        tabs.extend([
            {
                'id': -1,
                'name': '收藏题目',
                'english_name': 'Favorites',
                'description': '收藏的题目',
                'level4_id': course_id,
                'is_active': True,
                'tab_type': 'favorites'
            },
            {
                'id': -2,
                'name': '错题本',
                'english_name': 'Wrong Questions',
                'description': '做错的题目',
                'level4_id': course_id,
                'is_active': True,
                'tab_type': 'wrong'
            },
            {
                'id': -3,
                'name': '答题统计',
                'english_name': 'Statistics',
                'description': '答题统计信息',
                'level4_id': course_id,
                'is_active': True,
                'tab_type': 'stats'
            }
        ])
        
        return jsonify({
            'success': True,
            'data': tabs,
            'course': course.to_dict()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取课程标签失败: {str(e)}'
        }), 500 