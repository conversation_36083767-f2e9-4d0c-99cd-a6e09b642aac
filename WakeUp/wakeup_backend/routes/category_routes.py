from flask import Blueprint, request, jsonify
import json
from models.category_level1 import CategoryLevel1
from models.category_level2 import CategoryLevel2
from models.category_level3 import CategoryLevel3
from models.category_level4 import CategoryLevel4
from models.question import Question
from models.region import Region
from models.region_category import RegionCategory
from models import db
from models.user import User
from sqlalchemy import and_
import time

category_bp = Blueprint('category', __name__)

@category_bp.route('/api/categories/level1', methods=['GET'])
def get_categories():
    """获取一级分类，支持按地区筛选"""
    region_code = request.args.get('region_code')
    
    if region_code:
        # 获取指定地区的一级分类
        region = Region.query.filter_by(code=region_code, is_active=True).first()
        if not region:
            return jsonify({'error': f'地区 {region_code} 不存在或已禁用'}), 404
        
        # 获取该地区关联的一级分类
        region_categories = db.session.query(RegionCategory, CategoryLevel1).join(
            CategoryLevel1, 
            and_(
                RegionCategory.category_id == CategoryLevel1.id,
                RegionCategory.category_level == 1
            )
        ).filter(
            RegionCategory.region_id == region.id,
            RegionCategory.is_active == True,
            CategoryLevel1.is_active == True
        ).order_by(RegionCategory.display_order, CategoryLevel1.name).all()
        
        categories = []
        for region_cat, category in region_categories:
            cat_dict = category.to_dict()
            cat_dict['display_order'] = region_cat.display_order
            categories.append(cat_dict)
        
        return jsonify(categories)
    else:
        # 获取所有一级分类（原有逻辑）
        categories = CategoryLevel1.query.all()
        result = [c.to_dict() for c in categories]
        return jsonify(result)

@category_bp.route('/api/categories/level1', methods=['POST'])
def create_category():
    data = request.get_json()
    new_category = CategoryLevel1(name=data['name'])
    db.session.add(new_category)
    db.session.commit()
    return jsonify({'message': 'Category created successfully'})

@category_bp.route('/api/categories/level2', methods=['GET'])
def get_categories_level2():
    """获取二级分类，支持按地区和一级分类筛选"""
    level1_id = request.args.get('level1_id', type=int)
    region_code = request.args.get('region_code')
    
    if region_code:
        # 按地区获取二级分类
        region = Region.query.filter_by(code=region_code, is_active=True).first()
        if not region:
            return jsonify({'error': f'地区 {region_code} 不存在或已禁用'}), 404
        
        # 构建查询条件
        query_conditions = [
            RegionCategory.region_id == region.id,
            RegionCategory.category_level == 2,
            RegionCategory.is_active == True,
            CategoryLevel2.is_active == True
        ]
        
        if level1_id:
            query_conditions.append(CategoryLevel2.level1_id == level1_id)
        
        # 获取该地区关联的二级分类
        region_categories = db.session.query(RegionCategory, CategoryLevel2).join(
            CategoryLevel2,
            and_(
                RegionCategory.category_id == CategoryLevel2.id,
                RegionCategory.category_level == 2
            )
        ).filter(
            and_(*query_conditions)
        ).order_by(RegionCategory.display_order, CategoryLevel2.name).all()
        
        categories = []
        for region_cat, category in region_categories:
            cat_dict = category.to_dict()
            cat_dict['display_order'] = region_cat.display_order
            categories.append(cat_dict)
        
        return jsonify(categories)
    else:
        # 原有逻辑：获取所有二级分类
        query = CategoryLevel2.query
        if level1_id:
            query = query.filter_by(level1_id=level1_id)
        categories = query.all()
        result = [c.to_dict() for c in categories]
        return jsonify(result)

@category_bp.route('/api/categories/level3', methods=['GET'])
def get_categories_level3():
    """获取三级分类"""
    level2_id = request.args.get('level2_id', type=int)
    parent_id = request.args.get('parent_id', type=int)
    
    # 使用parent_id作为level2_id的备选
    category_id = level2_id or parent_id
    
    if not category_id:
        return jsonify({'error': '需要提供level2_id或parent_id参数'}), 400
    
    try:
        # 查询三级分类
        categories = CategoryLevel3.query.filter_by(
            level2_id=category_id, 
            is_active=True
        ).order_by(CategoryLevel3.name).all()
        
        result = [c.to_dict() for c in categories]
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'获取三级分类失败: {str(e)}'}), 500

@category_bp.route('/api/categories/level4', methods=['GET'])
def get_categories_level4():
    """获取四级分类"""
    level3_id = request.args.get('level3_id', type=int)
    parent_id = request.args.get('parent_id', type=int)
    
    # 使用parent_id作为level3_id的备选
    category_id = level3_id or parent_id
    
    if not category_id:
        return jsonify({'error': '需要提供level3_id或parent_id参数'}), 400
    
    try:
        # 查询四级分类
        categories = CategoryLevel4.query.filter_by(
            level3_id=category_id,
            is_active=True
        ).order_by(CategoryLevel4.name).all()
        
        result = [c.to_dict() for c in categories]
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': f'获取四级分类失败: {str(e)}'}), 500

@category_bp.route('/api/questions', methods=['GET'])
def get_questions_by_category():
    """根据四级分类ID获取题目数据"""
    level4_id = request.args.get('level4_id', type=int)
    category_level4_id = request.args.get('category_level4_id', type=int)
    
    # 使用category_level4_id作为level4_id的备选
    category_id = level4_id or category_level4_id
    
    if not category_id:
        return jsonify({'error': '需要提供level4_id或category_level4_id参数'}), 400
    
    try:
        # 查询与四级分类关联的题目
        questions = Question.query.filter_by(
            category_level4_id=category_id
        ).all()

        # 如果没有找到题目，返回空列表
        result = []

        for q in questions:
            try:
                # 处理 options 字段（可能是双重编码的 JSON、字符串或 None）
                options_value = q.options
                if isinstance(options_value, str):
                    try:
                        # 先尝试直接解析
                        options_value = json.loads(options_value)
                    except json.JSONDecodeError:
                        # 如果失败，可能是双重编码，尝试先解码 Unicode 转义
                        try:
                            decoded_str = options_value.encode().decode('unicode_escape')
                            options_value = json.loads(decoded_str)
                        except Exception:
                            # 最后回退：保留原始字符串
                            pass

                # 处理 answer 字段（可能是字符串数字或 JSON）
                answer_value = q.answer
                if isinstance(answer_value, str):
                    try:
                        # 先尝试解析为 JSON
                        answer_value = json.loads(answer_value)
                    except json.JSONDecodeError:
                        # 如果是纯数字字符串，保持原样
                        if answer_value.isdigit():
                            answer_value = int(answer_value)
                        # 否则保持字符串

                item = {
                    "id": q.id,
                    "question_text": q.question_text,
                    "question_type": q.question_type,
                    "options": options_value if options_value is not None else [],
                    "answer": answer_value if answer_value is not None else None,
                    "explanation": q.explanation or "",
                    "difficulty": q.difficulty or "",
                    "category_level4_id": q.category_level4_id,
                }

                # 直接添加到结果中
                result.append(item)
            except Exception as item_err:
                # 跳过单条坏数据，继续处理其他数据
                print(f"序列化题目失败，question_id={getattr(q, 'id', None)}: {item_err}")

        return jsonify({
            'success': True,
            'data': result,
            'total': len(result)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取题目失败: {str(e)}'
        }), 500

@category_bp.route('/api/categories/level4/all', methods=['GET'])
def get_all_level4_categories():
    # 获取授权信息
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'message': '未提供有效的授权信息'}), 401
    
    # 获取所有四级分类
    categories = CategoryLevel4.query.all()
    result = [cat.to_dict() for cat in categories]
    
    return jsonify(result)

@category_bp.route('/api/categories/level4/update_english_name', methods=['POST'])
def update_level4_english_name():
    # 获取授权信息
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'message': '未提供有效的授权信息'}), 401
    
    token = auth_header.split(' ')[1]
    user = User.query.filter_by(token=token).first()
    if not user:
        return jsonify({'message': '无效的授权令牌'}), 401
    
    # 验证用户是否有管理员权限
    if user.role != 'admin':
        return jsonify({'message': '没有权限执行此操作'}), 403
    
    data = request.get_json()
    course_id = data.get('course_id')
    english_name = data.get('english_name')
    
    if not course_id:
        return jsonify({'message': '缺少课程ID'}), 400
    
    # 更新英文名称
    category = CategoryLevel4.query.get(course_id)
    if not category:
        return jsonify({'message': '未找到指定课程'}), 404
    
    category.english_name = english_name
    db.session.commit()
    
    return jsonify({'message': '更新成功', 'status': 'success'})

@category_bp.route('/api/categories/hierarchical', methods=['GET'])
def get_hierarchical_categories():
    """获取层次化的分类数据（用于课程选择页面）- 简化版本"""
    try:
        region_code = request.args.get('region_code')
        
        # 构建层次化的分类数据
        result = []
        
        # 由于新数据库结构使用统一的categories表，查询逻辑需要更新
        # 这里先检查是否存在新的categories表结构
        try:
            # 尝试使用新的统一categories表
            # 获取一级分类
            level1_categories = db.session.query(CategoryLevel1).filter_by(is_active=True).order_by(CategoryLevel1.name).all()
            
            for level1 in level1_categories:
                level1_data = {
                    'category': level1.name,
                    'id': level1.id,
                    'courses': []
                }
                
                # 获取该一级分类下的二级分类（课程）
                level2_categories = CategoryLevel2.query.filter_by(
                    level1_id=level1.id, 
                    is_active=True
                ).order_by(CategoryLevel2.name).all()
                
                for level2 in level2_categories:
                    level2_data = {
                        'id': level2.id,
                        'name': level2.name,
                        "description": level2.description or '欢迎学习这个课程的内容',
                        "highlight": level2.highlight or '推荐学习',
                        "english_name": level2.english_name
                    }
                    level1_data['courses'].append(level2_data)
                
                if level1_data['courses']:  # 只返回有课程的分类
                    result.append(level1_data)
            
        except Exception as e:
            print(f"使用旧数据库结构: {e}")
            # 如果新表不存在，回退到旧的分层接口逻辑
            return get_hierarchical_categories_old()
        
        return jsonify({
            'success': True,
            'data': result,
            'total': len(result)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分类层次数据失败: {str(e)}'
        }), 500

def get_hierarchical_categories_old():
    """旧的分层分类逻辑（兼容性）"""
    try:
        region_code = request.args.get('region_code')
        max_level = request.args.get('max_level', 4, type=int)  # 现在最大到4级
        
        # 构建层次化的分类数据
        result = []
        
        if region_code:
            # 按地区获取分类
            region = Region.query.filter_by(code=region_code, is_active=True).first()
            if not region:
                return jsonify({'error': f'地区 {region_code} 不存在或已禁用'}), 404
            
            # 获取该地区的一级分类
            region_categories = db.session.query(RegionCategory, CategoryLevel1).join(
                CategoryLevel1, 
                and_(
                    RegionCategory.category_id == CategoryLevel1.id,
                    RegionCategory.category_level == 1
                )
            ).filter(
                RegionCategory.region_id == region.id,
                RegionCategory.is_active == True,
                CategoryLevel1.is_active == True
            ).order_by(RegionCategory.display_order, CategoryLevel1.name).all()
            
            for region_cat, level1 in region_categories:
                level1_data = _build_category_hierarchy(level1, max_level, region.id)
                result.append(level1_data)
        else:
            # 获取所有一级分类
            level1_categories = CategoryLevel1.query.filter_by(is_active=True).order_by(CategoryLevel1.name).all()
            
            for level1 in level1_categories:
                level1_data = _build_category_hierarchy(level1, max_level)
                result.append(level1_data)
        
        return jsonify({
            'success': True,
            'data': result,
            'total': len(result)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分类层次数据失败: {str(e)}'
        }), 500

def _build_category_hierarchy(level1_category, max_level=4, region_id=None):
    """构建分类层次结构"""
    # 一级分类基础数据
    level1_data = {
        'category': level1_category.name,
        'id': level1_category.id,
        'courses': []
    }
    
    if max_level >= 2:
        # 获取二级分类
        level2_query = CategoryLevel2.query.filter_by(
            level1_id=level1_category.id, 
            is_active=True
        )
        
        if region_id:
            # 如果指定了地区，只获取该地区的二级分类
            level2_categories = db.session.query(CategoryLevel2).join(
                RegionCategory,
                and_(
                    RegionCategory.category_id == CategoryLevel2.id,
                    RegionCategory.category_level == 2,
                    RegionCategory.region_id == region_id,
                    RegionCategory.is_active == True
                )
            ).filter(
                CategoryLevel2.level1_id == level1_category.id,
                CategoryLevel2.is_active == True
            ).order_by(CategoryLevel2.name).all()
        else:
            level2_categories = level2_query.order_by(CategoryLevel2.name).all()
        
        for level2 in level2_categories:
            level2_data = {
                'id': level2.id,
                'name': level2.name,
                'description': level2.description or '欢迎学习这个分类的内容',
                'highlight': level2.highlight or '推荐入门',
                'english_name': level2.english_name
            }
            
            if max_level >= 3:
                # 递归获取更深层级
                level2_data['subcategories'] = _get_subcategories(level2, 3, max_level, region_id)
            
            level1_data['courses'].append(level2_data)
    
    return level1_data

def _get_subcategories(parent_category, current_level, max_level, region_id=None):
    """递归获取子分类"""
    if current_level > max_level:
        return []
    
    subcategories = []
    
    if current_level == 3:
        # 三级分类
        query = CategoryLevel3.query.filter_by(
            level2_id=parent_category.id,
            is_active=True
        ).order_by(CategoryLevel3.name)
        
        for cat in query.all():
            cat_data = {
                'id': cat.id,
                'name': cat.name,
                'description': getattr(cat, 'description', ''),
                'highlight': getattr(cat, 'highlight', ''),
                'english_name': getattr(cat, 'english_name', None)
            }
            
            if max_level >= 4:
                cat_data['subcategories'] = _get_subcategories(cat, 4, max_level, region_id)
            
            subcategories.append(cat_data)
    
    elif current_level == 4:
        # 四级分类 - 这里添加题目统计信息
        query = CategoryLevel4.query.filter_by(
            level3_id=parent_category.id,
            is_active=True
        ).order_by(CategoryLevel4.name)
        
        for cat in query.all():
            # 统计该四级分类下的题目数量
            question_count = Question.query.filter_by(category_level4_id=cat.id).count()
            
            cat_data = {
                'id': cat.id,
                'name': cat.name,
                'description': getattr(cat, 'description', ''),
                'highlight': getattr(cat, 'highlight', ''),
                'english_name': getattr(cat, 'english_name', None),
                'question_count': question_count  # 添加题目数量
            }
            
            subcategories.append(cat_data)
    
    return subcategories

# 保留兼容性的API端点，但不再使用
@category_bp.route('/api/question_sets', methods=['GET'])
def get_question_sets_by_category():
    """根据分类ID获取题库数据 - 保留兼容性"""
    category_id = request.args.get('category_id', type=int)
    
    if not category_id:
        return jsonify({'error': '需要提供category_id参数'}), 400
    
    # 重定向到新的questions API
    try:
        # 查找该二级分类下的第一个四级分类
        level4_cat = db.session.query(CategoryLevel4).join(
            CategoryLevel3, CategoryLevel4.level3_id == CategoryLevel3.id
        ).filter(CategoryLevel3.level2_id == category_id).first()
        
        if level4_cat:
            # 获取该四级分类下的题目
            questions = Question.query.filter_by(category_level4_id=level4_cat.id).all()
            
            result = []
            for q in questions:
                result.append({
                    "id": q.id,
                    "name": f"题目{q.id}",  # 兼容旧格式
                    "description": q.question_text,
                    "difficulty": q.difficulty,
                    "course_id": category_id,
                    "is_active": True
                })
            
            return jsonify({
                'success': True,
                'data': result,
                'total': len(result)
            })
        else:
            # 如果没有四级分类，返回空数据
            return jsonify({
                'success': True,
                'data': [],
                'total': 0
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取题库失败: {str(e)}'
        }), 500