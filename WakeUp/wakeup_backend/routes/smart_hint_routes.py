"""
智能提示API路由
提供各种题型的智能提示接口
"""
from flask import Blueprint, request, jsonify
from models.question import Question
from services.smart_hint_service import SmartHintService
from utils.auth import token_required
import json

smart_hint_bp = Blueprint('smart_hint', __name__)


@smart_hint_bp.route('/api/smart_hints/fill_blank/<int:question_id>', methods=['GET'])
@token_required
def get_fill_blank_hints(current_user, question_id):
    """
    获取填空题智能提示
    """
    try:
        question = Question.query.get_or_404(question_id)
        user_input = request.args.get('input', '')
        
        hints = SmartHintService.get_fill_blank_hints(question, user_input)
        
        return jsonify({
            'success': True,
            'hints': hints
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取填空题提示失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/short_answer/<int:question_id>', methods=['GET'])
@token_required
def get_short_answer_templates(current_user, question_id):
    """
    获取简答题回答模板
    """
    try:
        question = Question.query.get_or_404(question_id)
        
        templates = SmartHintService.get_short_answer_templates(question)
        
        return jsonify({
            'success': True,
            'templates': templates
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取简答题模板失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/programming/<int:question_id>', methods=['GET'])
@token_required
def get_programming_snippets(current_user, question_id):
    """
    获取编程题代码片段
    """
    try:
        question = Question.query.get_or_404(question_id)
        language = request.args.get('language', 'python')
        
        snippets = SmartHintService.get_programming_snippets(question, language)
        
        return jsonify({
            'success': True,
            'snippets': snippets
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取编程题代码片段失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/matching/<int:question_id>', methods=['GET'])
@token_required
def get_matching_suggestions(current_user, question_id):
    """
    获取匹配题智能建议
    """
    try:
        question = Question.query.get_or_404(question_id)
        
        suggestions = SmartHintService.get_matching_suggestions(question)
        
        return jsonify({
            'success': True,
            'suggestions': suggestions
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取匹配题建议失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/ordering/<int:question_id>', methods=['GET'])
@token_required
def get_ordering_suggestions(current_user, question_id):
    """
    获取排序题智能建议
    """
    try:
        question = Question.query.get_or_404(question_id)
        
        suggestions = SmartHintService.get_ordering_suggestions(question)
        
        return jsonify({
            'success': True,
            'suggestions': suggestions
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取排序题建议失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/calculation/<int:question_id>', methods=['GET'])
@token_required
def get_calculation_hints(current_user, question_id):
    """
    获取计算题提示
    """
    try:
        question = Question.query.get_or_404(question_id)
        user_input = request.args.get('input', '')
        
        hints = SmartHintService.get_calculation_hints(question, user_input)
        
        return jsonify({
            'success': True,
            'hints': hints
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取计算题提示失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/auto_match/<int:question_id>', methods=['POST'])
@token_required
def auto_match_question(current_user, question_id):
    """
    自动匹配题目（匹配题专用）
    """
    try:
        question = Question.query.get_or_404(question_id)
        
        if not question.matchingItems:
            return jsonify({
                'success': False,
                'message': '该题目不是匹配题或没有匹配项'
            }), 400
        
        # 实现简单的自动匹配逻辑
        # 实际应用中可以使用更复杂的算法（如基于语义相似度）
        auto_matches = {}
        matching_items = json.loads(question.matchingItems) if isinstance(question.matchingItems, str) else question.matchingItems
        
        for item in matching_items:
            auto_matches[item['left']] = item['right']
        
        return jsonify({
            'success': True,
            'matches': auto_matches
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'自动匹配失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/auto_sort/<int:question_id>', methods=['POST'])
@token_required
def auto_sort_question(current_user, question_id):
    """
    自动排序题目（排序题专用）
    """
    try:
        question = Question.query.get_or_404(question_id)
        data = request.get_json() or {}
        sort_type = data.get('sort_type', 'correct')  # correct, numeric, alphabetic, time, reverse
        
        if not question.orderingItems:
            return jsonify({
                'success': False,
                'message': '该题目不是排序题或没有排序项'
            }), 400
        
        ordering_items = json.loads(question.orderingItems) if isinstance(question.orderingItems, str) else question.orderingItems
        
        if sort_type == 'correct':
            # 按正确顺序排序
            sorted_items = sorted(ordering_items, key=lambda x: x['correctOrder'])
        elif sort_type == 'numeric':
            # 按数值排序
            sorted_items = sorted(ordering_items, key=lambda x: float(x.get('numericValue', 0)))
        elif sort_type == 'alphabetic':
            # 按字母排序
            sorted_items = sorted(ordering_items, key=lambda x: x['content'])
        elif sort_type == 'reverse':
            # 逆序
            sorted_items = list(reversed(ordering_items))
        else:
            sorted_items = ordering_items
        
        return jsonify({
            'success': True,
            'sorted_items': sorted_items
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'自动排序失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/comprehensive/<int:question_id>', methods=['GET'])
@token_required
def get_comprehensive_hints(current_user, question_id):
    """
    获取综合智能提示（根据题目类型返回相应提示）
    """
    try:
        question = Question.query.get_or_404(question_id)
        
        result = {
            'question_id': question_id,
            'question_type': question.question_type,
            'hints': {}
        }
        
        # 根据题目类型提供相应的提示
        if question.question_type in ['fill_blank']:
            result['hints']['fill_blank'] = SmartHintService.get_fill_blank_hints(question)
            
        elif question.question_type in ['short_answer']:
            result['hints']['templates'] = SmartHintService.get_short_answer_templates(question)
            
        elif question.question_type in ['programming']:
            language = request.args.get('language', 'python')
            result['hints']['snippets'] = SmartHintService.get_programming_snippets(question, language)
            
        elif question.question_type in ['matching']:
            result['hints']['matching'] = SmartHintService.get_matching_suggestions(question)
            
        elif question.question_type in ['ordering']:
            result['hints']['ordering'] = SmartHintService.get_ordering_suggestions(question)
            
        elif question.question_type in ['calculation']:
            result['hints']['calculation'] = SmartHintService.get_calculation_hints(question)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取综合提示失败: {str(e)}'
        }), 500


@smart_hint_bp.route('/api/smart_hints/update/<int:question_id>', methods=['PUT'])
@token_required
def update_question_hints(current_user, question_id):
    """
    更新题目的智能提示数据
    """
    try:
        question = Question.query.get_or_404(question_id)
        data = request.get_json()
        
        # 更新各种提示字段
        if 'hints' in data:
            question.hints = json.dumps(data['hints'], ensure_ascii=False)
        if 'templates' in data:
            question.templates = json.dumps(data['templates'], ensure_ascii=False)
        if 'auto_complete' in data:
            question.auto_complete = json.dumps(data['auto_complete'], ensure_ascii=False)
        if 'code_snippets' in data:
            question.code_snippets = json.dumps(data['code_snippets'], ensure_ascii=False)
        if 'matching_hints' in data:
            question.matching_hints = json.dumps(data['matching_hints'], ensure_ascii=False)
        if 'ordering_hints' in data:
            question.ordering_hints = json.dumps(data['ordering_hints'], ensure_ascii=False)
        
        from models import db
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '智能提示数据更新成功'
        })
        
    except Exception as e:
        from models import db
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新智能提示数据失败: {str(e)}'
        }), 500