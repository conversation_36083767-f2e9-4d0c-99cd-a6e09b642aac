# 贡献指南

本项目使用 Git 提交钩子与 CI 检查，约束 Dart 代码中禁止直接使用 `debugPrint/print`，请统一使用 AppLogger。

## 本地开发必做

1) 启用本地 Git 提交钩子（仓库根目录执行一次）

```bash
# 使用仓库自带的钩子目录
git config core.hooksPath .githooks
# 确保钩子可执行
chmod +x .githooks/pre-commit
```

2) 运行基础校验

```bash
# Flutter 端
cd wakeup_app
flutter pub get
flutter analyze

# 后端（如需要）
cd ../wakeup_backend
pip install -r requirements.txt
flake8 || echo "(可选) 未启用 flake8"
```

## 日志规范

- 禁止：`debugPrint(...)`、`print(...)`
- 代替：`AppLogger.info/warning/error/success/performance/preload`
- 如果需要调试，请使用 AppLogger 对应级别；上线前也可通过 `AppLogger.setDebugMode` 控制输出

## 常见问题

- 暂存区存在 `debugPrint/print` 会被 pre-commit 阻止提交；如需临时绕过（不建议）：
  - `git commit --no-verify`
- CI 会在远端二次检查，仍然会失败，请及时修复日志调用

## 目录结构（简）

- `wakeup_app/`: Flutter 客户端
- `wakeup_backend/`: Flask 后端
- `.githooks/pre-commit`: 本地 Git 钩子脚本（需启用）

