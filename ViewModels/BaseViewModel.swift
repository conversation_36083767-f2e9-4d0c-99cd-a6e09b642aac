//
//  BaseViewModel.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import Foundation
import Combine

// MARK: - Base ViewModel Protocol
protocol BaseViewModel: ObservableObject {
    var isLoading: Bool { get set }
    var errorMessage: String? { get set }
    var cancellables: Set<AnyCancellable> { get set }
    
    func handleError(_ error: Error)
    func setLoading(_ loading: Bool)
}

// MARK: - Default Implementation
extension BaseViewModel {
    func handleError(_ error: Error) {
        DispatchQueue.main.async {
            self.isLoading = false
            if let appError = error as? AppError {
                self.errorMessage = appError.localizedDescription
            } else {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func setLoading(_ loading: Bool) {
        DispatchQueue.main.async {
            self.isLoading = loading
            if loading {
                self.errorMessage = nil
            }
        }
    }
}

// MARK: - ViewState Enum
enum ViewState: Equatable {
    case idle
    case loading
    case loaded
    case error(String)
    case empty
    
    var isLoading: Bool {
        if case .loading = self {
            return true
        }
        return false
    }
    
    var errorMessage: String? {
        if case .error(let message) = self {
            return message
        }
        return nil
    }
}
