//
//  LearningViewModel.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import Foundation
import Combine
import SwiftUI

@MainActor
class LearningViewModel: BaseViewModel {
    // MARK: - Published Properties
    @Published var viewState: ViewState = .idle
    @Published var courses: [Course] = []
    @Published var currentCourseIndex: Int = 0
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - Animation States
    @Published var titleOpacity: Double = 0
    @Published var cardsOffset: CGFloat = 50
    @Published var slideButtonScale: CGFloat = 0.8
    @Published var slideOffset: CGFloat = 0
    @Published var isSliding: Bool = false
    
    // MARK: - Dependencies
    private let authService: AuthService
    private let networkService: NetworkService
    private let toastManager: ToastManager
    
    // MARK: - Private Properties
    var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var isLoggedIn: Bool {
        authService.isLoggedIn
    }
    
    var currentCourse: Course? {
        courses.indices.contains(currentCourseIndex) ? courses[currentCourseIndex] : nil
    }
    
    var hasNoCourses: Bool {
        !isLoading && courses.isEmpty && isLoggedIn
    }
    
    // MARK: - Initialization
    init(
        authService: AuthService = AuthService.shared,
        networkService: NetworkService = NetworkService.shared,
        toastManager: ToastManager
    ) {
        self.authService = authService
        self.networkService = networkService
        self.toastManager = toastManager
        
        setupBindings()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // Listen to auth state changes
        authService.$isLoggedIn
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isLoggedIn in
                if isLoggedIn {
                    self?.loadCourses()
                } else {
                    self?.courses = []
                    self?.viewState = .idle
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    func onAppear() {
        loadCoursesIfNeeded()
        animateAppearance()
    }
    
    func loadCoursesIfNeeded() {
        if isLoggedIn && courses.isEmpty && viewState != .loading {
            loadCourses()
        }
    }
    
    func loadCourses() {
        guard let user = authService.currentUser,
              let token = user.token else {
            viewState = .error("用户未登录")
            return
        }
        
        viewState = .loading
        setLoading(true)
        
        networkService.fetchUserCourses(userId: user.id, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.setLoading(false)
                    
                    if case .failure(let error) = completion {
                        self?.handleNetworkError(error)
                    }
                },
                receiveValue: { [weak self] courseResponses in
                    let courses = courseResponses.map { Course.from($0) }
                    self?.courses = courses
                    
                    if courses.isEmpty {
                        self?.viewState = .empty
                    } else {
                        self?.viewState = .loaded
                        self?.toastManager.showSuccess("课程加载成功")
                        HapticManager.notification(.success)
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func selectCourse(at index: Int) {
        guard courses.indices.contains(index) else { return }
        
        withAnimation(.iOSSpring) {
            currentCourseIndex = index
        }
        
        HapticManager.selection()
    }
    
    func startLearning() {
        guard let course = currentCourse else { return }
        
        HapticManager.impact(.heavy)
        
        // TODO: Navigate to quiz page
        print("Starting learning for course: \(course.courseName)")
        toastManager.showInfo("开始学习：\(course.courseName)")
    }
    
    func navigateToCourses() {
        HapticManager.impact(.medium)
        
        // TODO: Navigate to courses page
        print("Navigating to courses page")
        toastManager.showInfo("前往课程页面")
    }
    
    // MARK: - Animation Methods
    func animateAppearance() {
        withAnimation(.easeOut(duration: 0.8)) {
            titleOpacity = 1
        }
        
        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2)) {
            cardsOffset = 0
        }
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.4)) {
            slideButtonScale = 1.0
        }
    }
    
    func updateSlideOffset(_ offset: CGFloat) {
        slideOffset = min(offset, 150)
        
        // Add haptic feedback when starting to drag
        if slideOffset > 10 && slideOffset < 15 && !isSliding {
            HapticManager.selection()
        }
    }
    
    func handleSlideEnd(translation: CGFloat, action: @escaping () -> Void) {
        if translation > 100 && !isSliding {
            isSliding = true
            
            withAnimation(.iOSEaseOut) {
                slideOffset = 150
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                action()
                self.resetSlideButton()
            }
        } else {
            withAnimation(.iOSSpring) {
                slideOffset = 0
            }
        }
    }
    
    func resetSlideButton() {
        withAnimation(.iOSSpring) {
            slideOffset = 0
            isSliding = false
        }
    }
    
    // MARK: - Private Methods
    private func handleNetworkError(_ error: NetworkError) {
        switch error {
        case .unauthorized:
            viewState = .error("登录已过期，请重新登录")
            toastManager.showError("登录已过期，请重新登录")
        case .networkError(let networkError):
            viewState = .error("网络连接失败")
            toastManager.showError("网络连接失败")
        default:
            viewState = .error("加载失败，请重试")
            toastManager.showError("加载失败，请重试")
        }
        
        HapticManager.notification(.error)
    }
}
