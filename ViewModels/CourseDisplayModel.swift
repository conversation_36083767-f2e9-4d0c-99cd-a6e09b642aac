//
//  CourseDisplayModel.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import Foundation

// MARK: - Course Display Model
struct CourseDisplayModel {
    let course: Course
    
    init(_ course: Course) {
        self.course = course
    }
    
    // MARK: - Display Properties
    var displayTag: String {
        return course.tag?.uppercased() ?? "LEARNING PROGRESS"
    }
    
    var displaySubtitle: String {
        return course.subtitle ?? "WakeUp Skill Boosting"
    }
    
    var displayEnglishName: String {
        return course.englishName ?? "Learning course \(course.id)"
    }
    
    var displayCourseName: String {
        return course.courseName
    }
    
    var displayDescription: String {
        return course.description ?? "暂无描述"
    }
    
    // MARK: - Progress (Mock data for now)
    var progressPercentage: Int {
        // TODO: Replace with actual progress data
        return Int.random(in: 30...90)
    }
    
    var progressText: String {
        return "\(progressPercentage)%"
    }
    
    var progressWidth: CGFloat {
        return CGFloat(progressPercentage) * 2.0 // Adjust multiplier as needed
    }
}

// MARK: - Array Extension
extension Array where Element == Course {
    var displayModels: [CourseDisplayModel] {
        return self.map { CourseDisplayModel($0) }
    }
}
