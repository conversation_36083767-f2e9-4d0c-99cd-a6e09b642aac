//
//  LearningHomePreview.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

#Preview("Learning Home - With Courses") {
    LearningHomeView()
        .environmentObject(ToastManager())
        .onAppear {
            // Mock data for preview
            let mockCourses = [
                Course(
                    id: 1,
                    courseName: "Python 入门实战课",
                    englishName: "Python Fundamentals",
                    description: "从零开始学习Python编程",
                    tag: "PROGRAMMING",
                    subtitle: "WakeUp Skill Boosting"
                ),
                Course(
                    id: 2,
                    courseName: "Swift iOS 开发",
                    englishName: "Swift iOS Development",
                    description: "学习使用Swift开发iOS应用",
                    tag: "MOBILE DEV",
                    subtitle: "WakeUp Skill Boosting"
                ),
                Course(
                    id: 3,
                    courseName: "机器学习基础",
                    englishName: "Machine Learning Basics",
                    description: "掌握机器学习的核心概念",
                    tag: "AI & ML",
                    subtitle: "WakeUp Skill Boosting"
                )
            ]
        }
}

#Preview("Learning Home - Loading") {
    GeometryReader { geometry in
        ZStack {
            // Background gradient
            LinearGradient(
                colors: [
                    Color.black,
                    Color(red: 0.08, green: 0.08, blue: 0.08),
                    Color(red: 0.2, green: 0, blue: 0)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            LearningLoadingView()
        }
    }
    .ignoresSafeArea()
}

#Preview("Learning Home - No Courses") {
    GeometryReader { geometry in
        ZStack {
            // Background gradient
            LinearGradient(
                colors: [
                    Color.black,
                    Color(red: 0.08, green: 0.08, blue: 0.08),
                    Color(red: 0.2, green: 0, blue: 0)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            
            VStack(spacing: 24) {
                Spacer()
                
                Image(systemName: "book.circle")
                    .font(.system(size: 64))
                    .foregroundColor(.white)
                
                Text("暂无课程")
                    .font(.largeTitle)
                    .fontWeight(.black)
                    .foregroundColor(.white)
                
                Text("选择您感兴趣的课程")
                    .font(.title2)
                    .fontWeight(.bold)
                    .gradientText()
                
                // Mock slide button
                ZStack {
                    RoundedRectangle(cornerRadius: 50)
                        .fill(Color.black.opacity(0.54))
                        .frame(height: 84)
                    
                    HStack {
                        Circle()
                            .fill(Color.redGradient())
                            .frame(width: 60, height: 60)
                            .overlay(
                                Image(systemName: "chevron.right")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            )
                        
                        Spacer()
                        
                        Text("前往课程页面")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 12)
                }
                .padding(.horizontal, 24)
                
                Spacer()
            }
        }
    }
    .ignoresSafeArea()
}

#Preview("Course Card") {
    let sampleCourse = Course(
        id: 1,
        courseName: "Python 入门实战课",
        englishName: "Python Fundamentals",
        description: "从零开始学习Python编程",
        tag: "PROGRAMMING",
        subtitle: "WakeUp Skill Boosting"
    )
    
    return CourseCardView(course: sampleCourse, isSelected: true)
        .padding()
        .background(Color.darkGradient())
}

#Preview("Toast Examples") {
    VStack(spacing: 20) {
        ToastView(message: "课程加载成功", type: .success)
        ToastView(message: "网络连接失败", type: .error)
        ToastView(message: "正在同步数据...", type: .info)
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
