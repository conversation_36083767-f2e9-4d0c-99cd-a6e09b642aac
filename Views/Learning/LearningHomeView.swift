//
//  LearningHomeView.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI
import Combine

struct LearningHomeView: View {
    @StateObject private var authService = AuthService.shared
    @StateObject private var networkService = NetworkService.shared
    @EnvironmentObject private var toastManager: ToastManager
    @State private var courses: [Course] = []
    @State private var currentCourseIndex = 0
    @State private var isLoading = false
    @State private var appError: AppError?
    @State private var slideOffset: CGFloat = 0
    @State private var isSliding = false
    @State private var cancellables = Set<AnyCancellable>()

    // Animation states
    @State private var titleOpacity: Double = 0
    @State private var cardsOffset: CGFloat = 50
    @State private var slideButtonScale: CGFloat = 0.8

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient
                backgroundGradient(geometry: geometry)

                if !authService.isLoggedIn {
                    notLoggedInView
                } else if isLoading {
                    loadingView
                } else if appError != nil {
                    errorView
                } else if courses.isEmpty {
                    noCoursesView
                } else {
                    mainContentView(geometry: geometry)
                }
            }
        }
        .ignoresSafeArea()
        .errorAlert($appError)
        .onAppear {
            loadCoursesIfNeeded()
            animateAppearance()
        }
        .onChange(of: authService.isLoggedIn) { _, isLoggedIn in
            if isLoggedIn {
                loadCourses()
            } else {
                courses = []
            }
        }
    }

    // MARK: - Background Gradient
    private func backgroundGradient(geometry: GeometryProxy) -> some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color.black,
                    Color(red: 0.08, green: 0.08, blue: 0.08),
                    Color(red: 0.2, green: 0, blue: 0)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Top right accent
            RadialGradient(
                colors: [
                    Color(red: 0.73, green: 0, blue: 0).opacity(0.6),
                    Color.clear
                ],
                center: UnitPoint(x: 0.9, y: 0.1),
                startRadius: 0,
                endRadius: geometry.size.width * 0.6
            )

            // Bottom right accent
            RadialGradient(
                colors: [
                    Color(red: 0.8, green: 0, blue: 0).opacity(0.4),
                    Color.clear
                ],
                center: UnitPoint(x: 0.6, y: 0.9),
                startRadius: 0,
                endRadius: geometry.size.width * 0.8
            )
        }
    }

    // MARK: - Not Logged In View
    private var notLoggedInView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "lock.circle")
                .font(.system(size: 64))
                .foregroundColor(.white)
                .opacity(titleOpacity)

            Text("请登录以开始学习")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .opacity(titleOpacity)

            Button(action: {
                // Navigate to login
            }) {
                Text("立即登录")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 30)
                            .fill(Color(red: 0.67, green: 0, blue: 0))
                    )
            }
            .scaleEffect(slideButtonScale)

            Spacer()
        }
        .padding(.horizontal, 24)
    }

    // MARK: - Loading View
    private var loadingView: some View {
        LearningLoadingView()
    }

    // MARK: - Error View
    private var errorView: some View {
        VStack(spacing: 24) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.red)
                .bounceAnimation(duration: 2.0, amplitude: 5)

            Text(appError?.localizedDescription ?? "未知错误")
                .appBody()
                .multilineTextAlignment(.center)

            if let suggestion = appError?.recoverySuggestion {
                Text(suggestion)
                    .appCaption()
                    .multilineTextAlignment(.center)
            }

            Button(action: {
                HapticManager.impact(.medium)
                loadCourses()
            }) {
                Text("重试")
                    .iOSButton(backgroundColor: .primaryRed)
            }
        }
        .padding(.horizontal, 24)
        .slideIn(from: .bottom, delay: 0.3)
    }

    // MARK: - No Courses View
    private var noCoursesView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "book.circle")
                .font(.system(size: 64))
                .foregroundColor(.white)
                .opacity(titleOpacity)

            Text("暂无课程")
                .font(.largeTitle)
                .fontWeight(.black)
                .foregroundColor(.white)
                .opacity(titleOpacity)

            Text("选择您感兴趣的课程")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color(red: 1, green: 0.2, blue: 0.2), Color(red: 0.6, green: 0, blue: 0)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .opacity(titleOpacity)

            slideToActionButton(text: "前往课程页面") {
                // Navigate to courses
            }

            Spacer()
        }
        .padding(.horizontal, 24)
    }

    // MARK: - Main Content View
    private func mainContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // Header with title and avatar
            headerView
                .opacity(titleOpacity)
                .slideIn(from: .top, delay: 0.2)

            // Course cards carousel
            if courses.count > 1 {
                CourseCardCarousel(courses: courses, currentIndex: $currentCourseIndex)
                    .frame(height: 220)
                    .opacity(titleOpacity)
                    .slideIn(from: .trailing, delay: 0.4)
                    .padding(.top, 20)
            }

            Spacer()

            // Course content
            courseContentView(geometry: geometry)
                .offset(y: cardsOffset)
                .slideIn(from: .bottom, delay: 0.6)
        }
        .padding(.top, geometry.safeAreaInsets.top)
    }

    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Text("学习")
                .font(.largeTitle)
                .fontWeight(.black)
                .foregroundColor(.white)

            Spacer()

            // Avatar button placeholder
            Button(action: {}) {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 36, height: 36)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(.white)
                    )
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
    }

    // MARK: - Course Content View
    private func courseContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 15) {
            // Course title and info
            courseInfoView

            // Slide to start button
            slideToActionButton(text: "开始学习") {
                navigateToQuiz()
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 80)
    }

    // MARK: - Course Info View
    private var courseInfoView: some View {
        let currentCourse = courses[safe: currentCourseIndex]

        return VStack(alignment: .leading, spacing: 5) {
            Text("课程")
                .font(.largeTitle)
                .fontWeight(.black)
                .foregroundColor(.white)

            Text(currentCourse?.courseName ?? "课程")
                .font(.largeTitle)
                .fontWeight(.black)
                .foregroundColor(.white)

            Text(currentCourse?.displayEnglishName ?? "Learning Course")
                .font(.title2)
                .fontWeight(.black)
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color(red: 1, green: 0.2, blue: 0.2), Color(red: 0.6, green: 0, blue: 0)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // MARK: - Slide to Action Button
    private func slideToActionButton(text: String, action: @escaping () -> Void) -> some View {
        ZStack {
            // Background with glassmorphism effect
            RoundedRectangle(cornerRadius: 50)
                .fill(Color.black.opacity(0.54))
                .background(
                    RoundedRectangle(cornerRadius: 50)
                        .fill(.ultraThinMaterial.opacity(0.3))
                )
                .frame(height: 84)

            HStack {
                // Slide button with enhanced styling
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(red: 0.8, green: 0, blue: 0),
                                Color(red: 0.6, green: 0, blue: 0)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 60, height: 60)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
                    .overlay(
                        Image(systemName: "chevron.right")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                    )
                    .offset(x: slideOffset)
                    .scaleEffect(slideButtonScale)
                    .shadow(color: Color(red: 0.6, green: 0, blue: 0).opacity(0.5), radius: 8, x: 0, y: 4)

                Spacer()

                // Text with fade effect
                Text(text)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .opacity(1 - slideOffset / 150)
                    .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)

                Spacer()
            }
            .padding(.horizontal, 12)
        }
        .scaleOnPress(scale: 0.98)
        .gesture(
            DragGesture()
                .onChanged { value in
                    if !isSliding && value.translation.x > 0 {
                        slideOffset = min(value.translation.x, 150)

                        // Add haptic feedback when starting to drag
                        if slideOffset > 10 && slideOffset < 15 {
                            HapticManager.selection()
                        }
                    }
                }
                .onEnded { value in
                    if value.translation.x > 100 && !isSliding {
                        // Trigger action with haptic feedback
                        HapticManager.impact(.heavy)
                        isSliding = true

                        withAnimation(.iOSEaseOut) {
                            slideOffset = 150
                        }

                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            action()
                            resetSlideButton()
                        }
                    } else {
                        // Reset position with spring animation
                        withAnimation(.iOSSpring) {
                            slideOffset = 0
                        }
                    }
                }
        )
    }

    // MARK: - Helper Methods
    private func loadCoursesIfNeeded() {
        if authService.isLoggedIn && courses.isEmpty {
            loadCourses()
        }
    }

    private func loadCourses() {
        guard let user = authService.currentUser,
              let token = user.token else {
            appError = .authenticationError
            return
        }

        isLoading = true
        appError = nil

        networkService.fetchUserCourses(userId: user.id, token: token)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        switch error {
                        case .unauthorized:
                            self?.appError = .authenticationError
                            self?.toastManager.showError("登录已过期，请重新登录")
                        case .networkError(let networkError):
                            self?.appError = .networkError(networkError.localizedDescription)
                            self?.toastManager.showError("网络连接失败")
                        default:
                            self?.appError = .unknownError
                            self?.toastManager.showError("加载失败，请重试")
                        }
                        HapticManager.notification(.error)
                    }
                },
                receiveValue: { [weak self] courseResponses in
                    self?.courses = courseResponses.map { Course.from($0) }
                    if !courseResponses.isEmpty {
                        self?.toastManager.showSuccess("课程加载成功")
                        HapticManager.notification(.success)
                    }
                }
            )
            .store(in: &cancellables)
    }

    private func animateAppearance() {
        withAnimation(.easeOut(duration: 0.8)) {
            titleOpacity = 1
        }

        withAnimation(.spring(response: 0.8, dampingFraction: 0.8).delay(0.2)) {
            cardsOffset = 0
        }

        withAnimation(.spring(response: 0.6, dampingFraction: 0.7).delay(0.4)) {
            slideButtonScale = 1.0
        }
    }

    private func resetSlideButton() {
        withAnimation(.spring()) {
            slideOffset = 0
            isSliding = false
        }
    }

    private func navigateToQuiz() {
        // TODO: Implement navigation to quiz
        print("Navigate to quiz for course: \(courses[safe: currentCourseIndex]?.courseName ?? "Unknown")")
    }
}

// MARK: - Array Extension
extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
