//
//  LearningHomeView.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

struct LearningHomeView: View {
    @StateObject private var viewModel: LearningViewModel
    @EnvironmentObject private var toastManager: ToastManager

    init(toastManager: ToastManager = ToastManager()) {
        self._viewModel = StateObject(wrappedValue: LearningViewModel(toastManager: toastManager))
    }

    init(viewModel: LearningViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background gradient
                backgroundGradient(geometry: geometry)

                // Content based on view state
                contentView(geometry: geometry)
            }
        }
        .ignoresSafeArea()
        .onAppear {
            viewModel.onAppear()
        }
    }

    // MARK: - Content View
    @ViewBuilder
    private func contentView(geometry: GeometryProxy) -> some View {
        switch viewModel.viewState {
        case .idle:
            if !viewModel.isLoggedIn {
                notLoggedInView
            } else {
                EmptyView()
            }

        case .loading:
            loadingView

        case .loaded:
            mainContentView(geometry: geometry)

        case .error(let message):
            errorView(message: message)

        case .empty:
            noCoursesView
        }
    }

    // MARK: - Background Gradient
    private func backgroundGradient(geometry: GeometryProxy) -> some View {
        ZStack {
            // Base gradient
            LinearGradient(
                colors: [
                    Color.black,
                    Color(red: 0.08, green: 0.08, blue: 0.08),
                    Color(red: 0.2, green: 0, blue: 0)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )

            // Top right accent
            RadialGradient(
                colors: [
                    Color(red: 0.73, green: 0, blue: 0).opacity(0.6),
                    Color.clear
                ],
                center: UnitPoint(x: 0.9, y: 0.1),
                startRadius: 0,
                endRadius: geometry.size.width * 0.6
            )

            // Bottom right accent
            RadialGradient(
                colors: [
                    Color(red: 0.8, green: 0, blue: 0).opacity(0.4),
                    Color.clear
                ],
                center: UnitPoint(x: 0.6, y: 0.9),
                startRadius: 0,
                endRadius: geometry.size.width * 0.8
            )
        }
    }

    // MARK: - Not Logged In View
    private var notLoggedInView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "lock.circle")
                .font(.system(size: 64))
                .foregroundColor(.white)
                .opacity(viewModel.titleOpacity)

            Text("请登录以开始学习")
                .appTitle()
                .opacity(viewModel.titleOpacity)

            Button(action: {
                // TODO: Navigate to login
            }) {
                Text("立即登录")
                    .iOSButton(backgroundColor: .primaryRed)
            }
            .scaleEffect(viewModel.slideButtonScale)

            Spacer()
        }
        .padding(.horizontal, 24)
        .slideIn(from: .bottom, delay: 0.3)
    }

    // MARK: - Loading View
    private var loadingView: some View {
        LearningLoadingView()
    }

    // MARK: - Error View
    private func errorView(message: String) -> some View {
        VStack(spacing: 24) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 48))
                .foregroundColor(.red)
                .bounceAnimation(duration: 2.0, amplitude: 5)

            Text(message)
                .appBody()
                .multilineTextAlignment(.center)

            Button(action: {
                HapticManager.impact(.medium)
                viewModel.loadCourses()
            }) {
                Text("重试")
                    .iOSButton(backgroundColor: .primaryRed)
            }
        }
        .padding(.horizontal, 24)
        .slideIn(from: .bottom, delay: 0.3)
    }

    // MARK: - No Courses View
    private var noCoursesView: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "book.circle")
                .font(.system(size: 64))
                .foregroundColor(.white)
                .opacity(viewModel.titleOpacity)

            Text("暂无课程")
                .appTitle()
                .opacity(viewModel.titleOpacity)

            Text("选择您感兴趣的课程")
                .font(.title2)
                .fontWeight(.bold)
                .gradientText()
                .opacity(viewModel.titleOpacity)

            slideToActionButton(text: "前往课程页面") {
                viewModel.navigateToCourses()
            }

            Spacer()
        }
        .padding(.horizontal, 24)
        .slideIn(from: .bottom, delay: 0.3)
    }

    // MARK: - Main Content View
    private func mainContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 0) {
            // Header with title and avatar
            headerView
                .opacity(viewModel.titleOpacity)
                .slideIn(from: .top, delay: 0.2)

            // Course cards carousel
            if viewModel.courses.count > 1 {
                CourseCardCarousel(
                    courses: viewModel.courses,
                    currentIndex: Binding(
                        get: { viewModel.currentCourseIndex },
                        set: { viewModel.selectCourse(at: $0) }
                    )
                )
                .frame(height: 220)
                .opacity(viewModel.titleOpacity)
                .slideIn(from: .trailing, delay: 0.4)
                .padding(.top, 20)
            }

            Spacer()

            // Course content
            courseContentView(geometry: geometry)
                .offset(y: viewModel.cardsOffset)
                .slideIn(from: .bottom, delay: 0.6)
        }
        .padding(.top, geometry.safeAreaInsets.top)
    }

    // MARK: - Header View
    private var headerView: some View {
        HStack {
            Text("学习")
                .font(.largeTitle)
                .fontWeight(.black)
                .foregroundColor(.white)

            Spacer()

            // Avatar button placeholder
            Button(action: {}) {
                Circle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 36, height: 36)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(.white)
                    )
            }
        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
    }

    // MARK: - Course Content View
    private func courseContentView(geometry: GeometryProxy) -> some View {
        VStack(spacing: 15) {
            // Course title and info
            courseInfoView

            // Slide to start button
            slideToActionButton(text: "开始学习") {
                viewModel.startLearning()
            }
        }
        .padding(.horizontal, 24)
        .padding(.bottom, 80)
    }

    // MARK: - Course Info View
    private var courseInfoView: some View {
        if let currentCourse = viewModel.currentCourse {
            let displayModel = CourseDisplayModel(currentCourse)

            VStack(alignment: .leading, spacing: 5) {
                Text("课程")
                    .appTitle()

                Text(displayModel.displayCourseName)
                    .appTitle()

                Text(displayModel.displayEnglishName)
                    .font(.title2)
                    .fontWeight(.black)
                    .gradientText()
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        } else {
            EmptyView()
        }
    }

    // MARK: - Slide to Action Button
    private func slideToActionButton(text: String, action: @escaping () -> Void) -> some View {
        ZStack {
            // Background with glassmorphism effect
            RoundedRectangle(cornerRadius: 50)
                .fill(Color.black.opacity(0.54))
                .background(
                    RoundedRectangle(cornerRadius: 50)
                        .fill(.ultraThinMaterial.opacity(0.3))
                )
                .frame(height: 84)

            HStack {
                // Slide button with enhanced styling
                Circle()
                    .fill(Color.redGradient())
                    .frame(width: 60, height: 60)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
                    .overlay(
                        Image(systemName: "chevron.right")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)
                    )
                    .offset(x: viewModel.slideOffset)
                    .scaleEffect(viewModel.slideButtonScale)
                    .shadow(color: Color.primaryRed.opacity(0.5), radius: 8, x: 0, y: 4)

                Spacer()

                // Text with fade effect
                Text(text)
                    .appHeadline()
                    .opacity(1 - viewModel.slideOffset / 150)
                    .shadow(color: .black.opacity(0.3), radius: 1, x: 0, y: 1)

                Spacer()
            }
            .padding(.horizontal, 12)
        }
        .scaleOnPress(scale: 0.98)
        .gesture(
            DragGesture()
                .onChanged { value in
                    if !viewModel.isSliding && value.translation.x > 0 {
                        viewModel.updateSlideOffset(value.translation.x)
                    }
                }
                .onEnded { value in
                    viewModel.handleSlideEnd(translation: value.translation.x, action: action)
                }
        )
    }
}
