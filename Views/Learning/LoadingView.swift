//
//  LoadingView.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

struct LearningLoadingView: View {
    @State private var rotationAngle: Double = 0
    @State private var pulseScale: CGFloat = 1.0
    @State private var shimmerPhase: CGFloat = 0
    
    var body: some View {
        VStack(spacing: 30) {
            // Custom loading indicator
            ZStack {
                // Outer ring
                Circle()
                    .stroke(Color.white.opacity(0.2), lineWidth: 4)
                    .frame(width: 60, height: 60)
                
                // Animated arc
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(
                        LinearGradient(
                            colors: [
                                Color(red: 1, green: 0.2, blue: 0.2),
                                Color(red: 0.6, green: 0, blue: 0)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 60, height: 60)
                    .rotationEffect(.degrees(rotationAngle))
                
                // Center dot
                Circle()
                    .fill(Color.white)
                    .frame(width: 8, height: 8)
                    .scaleEffect(pulseScale)
            }
            
            // Loading text with shimmer
            Text("加载中...")
                .font(.headline)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .overlay(
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    Color.clear,
                                    Color.white.opacity(0.6),
                                    Color.clear
                                ],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .offset(x: shimmerPhase)
                        .clipped()
                )
            
            // Skeleton cards
            VStack(spacing: 16) {
                ForEach(0..<2, id: \.self) { _ in
                    skeletonCard
                }
            }
            .padding(.horizontal, 24)
        }
        .onAppear {
            startAnimations()
        }
    }
    
    private var skeletonCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Tag skeleton
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.white.opacity(0.1))
                .frame(width: 120, height: 12)
                .shimmer()
            
            // Title skeleton
            VStack(alignment: .leading, spacing: 8) {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.white.opacity(0.15))
                    .frame(height: 20)
                    .shimmer()
                
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.white.opacity(0.15))
                    .frame(width: 200, height: 20)
                    .shimmer()
            }
            
            // Subtitle skeleton
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.white.opacity(0.1))
                .frame(width: 160, height: 12)
                .shimmer()
            
            Spacer()
            
            // Progress skeleton
            HStack {
                RoundedRectangle(cornerRadius: 2)
                    .fill(Color.white.opacity(0.1))
                    .frame(height: 4)
                    .shimmer()
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.white.opacity(0.1))
                    .frame(width: 40, height: 12)
                    .shimmer()
            }
        }
        .padding(20)
        .frame(height: 160)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.black.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
    
    private func startAnimations() {
        // Rotation animation
        withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
            rotationAngle = 360
        }
        
        // Pulse animation
        withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
            pulseScale = 1.3
        }
        
        // Shimmer animation
        withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
            shimmerPhase = 300
        }
    }
}

// MARK: - Generic Skeleton View
struct SkeletonView: View {
    let width: CGFloat?
    let height: CGFloat
    let cornerRadius: CGFloat
    
    init(width: CGFloat? = nil, height: CGFloat = 20, cornerRadius: CGFloat = 6) {
        self.width = width
        self.height = height
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        RoundedRectangle(cornerRadius: cornerRadius)
            .fill(Color.white.opacity(0.15))
            .frame(width: width, height: height)
            .shimmer()
    }
}

#Preview {
    LearningLoadingView()
        .background(
            LinearGradient(
                colors: [Color.black, Color(red: 0.2, green: 0, blue: 0)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
}
