//
//  CourseCardView.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

struct CourseCardView: View {
    let course: Course
    let isSelected: Bool
    @State private var cardScale: CGFloat = 1.0
    @State private var cardOpacity: Double = 1.0
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Course tag
            Text(course.displayTag)
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white.opacity(0.8))
                .tracking(1.2)
            
            // Course name
            Text(course.courseName)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // Course subtitle
            Text(course.displaySubtitle)
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .lineLimit(1)
            
            Spacer()
            
            // Progress indicator (placeholder)
            HStack {
                RoundedRectangle(cornerRadius: 2)
                    .fill(Color.white.opacity(0.3))
                    .frame(height: 4)
                    .overlay(
                        HStack {
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color(red: 1, green: 0.2, blue: 0.2))
                                .frame(width: 60)
                            Spacer()
                        }
                    )
                
                Text("60%")
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))
            }
        }
        .padding(20)
        .frame(width: 280, height: 200)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.black.opacity(0.6),
                            Color(red: 0.1, green: 0.1, blue: 0.1).opacity(0.8)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.white.opacity(0.2),
                                    Color.clear
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
        )
        .scaleEffect(isSelected ? 1.0 : 0.9)
        .opacity(isSelected ? 1.0 : 0.7)
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isSelected)
        .onTapGesture {
            // Add haptic feedback
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred()
            
            // Scale animation
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                cardScale = 0.95
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                    cardScale = 1.0
                }
            }
        }
        .scaleEffect(cardScale)
    }
}

// MARK: - Course Card Carousel
struct CourseCardCarousel: View {
    let courses: [Course]
    @Binding var currentIndex: Int
    @State private var dragOffset: CGFloat = 0
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 20) {
                ForEach(Array(courses.enumerated()), id: \.element.id) { index, course in
                    CourseCardView(
                        course: course,
                        isSelected: index == currentIndex
                    )
                    .onTapGesture {
                        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                            currentIndex = index
                        }
                    }
                }
            }
            .padding(.horizontal, 24)
        }
        .scrollTargetBehavior(.paging)
    }
}

#Preview {
    let sampleCourse = Course(
        id: 1,
        courseName: "Python 入门实战课",
        englishName: "Python Fundamentals",
        description: "Learn Python programming from scratch",
        tag: "PROGRAMMING",
        subtitle: "WakeUp Skill Boosting"
    )
    
    return CourseCardView(course: sampleCourse, isSelected: true)
        .background(Color.black)
}
