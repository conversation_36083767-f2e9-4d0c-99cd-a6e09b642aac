// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		B62AEC802E5C92FD00CEAAA4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B62AEC662E5C92F800CEAAA4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B62AEC6D2E5C92F800CEAAA4;
			remoteInfo = QuizCube;
		};
		B62AEC8A2E5C92FD00CEAAA4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B62AEC662E5C92F800CEAAA4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B62AEC6D2E5C92F800CEAAA4;
			remoteInfo = QuizCube;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		B62AEC6E2E5C92F800CEAAA4 /* QuizCube.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = QuizCube.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B62AEC7F2E5C92FD00CEAAA4 /* QuizCubeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = QuizCubeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B62AEC892E5C92FD00CEAAA4 /* QuizCubeUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = QuizCubeUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		B62AEC912E5C92FD00CEAAA4 /* Exceptions for "QuizCube" folder in "QuizCube" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = B62AEC6D2E5C92F800CEAAA4 /* QuizCube */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B62AEC702E5C92F800CEAAA4 /* QuizCube */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B62AEC912E5C92FD00CEAAA4 /* Exceptions for "QuizCube" folder in "QuizCube" target */,
			);
			path = QuizCube;
			sourceTree = "<group>";
		};
		B62AEC822E5C92FD00CEAAA4 /* QuizCubeTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = QuizCubeTests;
			sourceTree = "<group>";
		};
		B62AEC8C2E5C92FD00CEAAA4 /* QuizCubeUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = QuizCubeUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B62AEC6B2E5C92F800CEAAA4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B62AEC7C2E5C92FD00CEAAA4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B62AEC862E5C92FD00CEAAA4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B62AEC652E5C92F800CEAAA4 = {
			isa = PBXGroup;
			children = (
				B62AEC702E5C92F800CEAAA4 /* QuizCube */,
				B62AEC822E5C92FD00CEAAA4 /* QuizCubeTests */,
				B62AEC8C2E5C92FD00CEAAA4 /* QuizCubeUITests */,
				B62AEC6F2E5C92F800CEAAA4 /* Products */,
			);
			sourceTree = "<group>";
		};
		B62AEC6F2E5C92F800CEAAA4 /* Products */ = {
			isa = PBXGroup;
			children = (
				B62AEC6E2E5C92F800CEAAA4 /* QuizCube.app */,
				B62AEC7F2E5C92FD00CEAAA4 /* QuizCubeTests.xctest */,
				B62AEC892E5C92FD00CEAAA4 /* QuizCubeUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B62AEC6D2E5C92F800CEAAA4 /* QuizCube */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B62AEC922E5C92FD00CEAAA4 /* Build configuration list for PBXNativeTarget "QuizCube" */;
			buildPhases = (
				B62AEC6A2E5C92F800CEAAA4 /* Sources */,
				B62AEC6B2E5C92F800CEAAA4 /* Frameworks */,
				B62AEC6C2E5C92F800CEAAA4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B62AEC702E5C92F800CEAAA4 /* QuizCube */,
			);
			name = QuizCube;
			packageProductDependencies = (
			);
			productName = QuizCube;
			productReference = B62AEC6E2E5C92F800CEAAA4 /* QuizCube.app */;
			productType = "com.apple.product-type.application";
		};
		B62AEC7E2E5C92FD00CEAAA4 /* QuizCubeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B62AEC972E5C92FD00CEAAA4 /* Build configuration list for PBXNativeTarget "QuizCubeTests" */;
			buildPhases = (
				B62AEC7B2E5C92FD00CEAAA4 /* Sources */,
				B62AEC7C2E5C92FD00CEAAA4 /* Frameworks */,
				B62AEC7D2E5C92FD00CEAAA4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B62AEC812E5C92FD00CEAAA4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B62AEC822E5C92FD00CEAAA4 /* QuizCubeTests */,
			);
			name = QuizCubeTests;
			packageProductDependencies = (
			);
			productName = QuizCubeTests;
			productReference = B62AEC7F2E5C92FD00CEAAA4 /* QuizCubeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B62AEC882E5C92FD00CEAAA4 /* QuizCubeUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B62AEC9A2E5C92FD00CEAAA4 /* Build configuration list for PBXNativeTarget "QuizCubeUITests" */;
			buildPhases = (
				B62AEC852E5C92FD00CEAAA4 /* Sources */,
				B62AEC862E5C92FD00CEAAA4 /* Frameworks */,
				B62AEC872E5C92FD00CEAAA4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B62AEC8B2E5C92FD00CEAAA4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B62AEC8C2E5C92FD00CEAAA4 /* QuizCubeUITests */,
			);
			name = QuizCubeUITests;
			packageProductDependencies = (
			);
			productName = QuizCubeUITests;
			productReference = B62AEC892E5C92FD00CEAAA4 /* QuizCubeUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B62AEC662E5C92F800CEAAA4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					B62AEC6D2E5C92F800CEAAA4 = {
						CreatedOnToolsVersion = 16.4;
					};
					B62AEC7E2E5C92FD00CEAAA4 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B62AEC6D2E5C92F800CEAAA4;
					};
					B62AEC882E5C92FD00CEAAA4 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B62AEC6D2E5C92F800CEAAA4;
					};
				};
			};
			buildConfigurationList = B62AEC692E5C92F800CEAAA4 /* Build configuration list for PBXProject "QuizCube" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B62AEC652E5C92F800CEAAA4;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = B62AEC6F2E5C92F800CEAAA4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B62AEC6D2E5C92F800CEAAA4 /* QuizCube */,
				B62AEC7E2E5C92FD00CEAAA4 /* QuizCubeTests */,
				B62AEC882E5C92FD00CEAAA4 /* QuizCubeUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B62AEC6C2E5C92F800CEAAA4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B62AEC7D2E5C92FD00CEAAA4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B62AEC872E5C92FD00CEAAA4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B62AEC6A2E5C92F800CEAAA4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B62AEC7B2E5C92FD00CEAAA4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B62AEC852E5C92FD00CEAAA4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B62AEC812E5C92FD00CEAAA4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B62AEC6D2E5C92F800CEAAA4 /* QuizCube */;
			targetProxy = B62AEC802E5C92FD00CEAAA4 /* PBXContainerItemProxy */;
		};
		B62AEC8B2E5C92FD00CEAAA4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B62AEC6D2E5C92F800CEAAA4 /* QuizCube */;
			targetProxy = B62AEC8A2E5C92FD00CEAAA4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B62AEC932E5C92FD00CEAAA4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = QuizCube/QuizCube.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DZBQWSC588;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = QuizCube/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navoriax.QuizCube;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		B62AEC942E5C92FD00CEAAA4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = QuizCube/QuizCube.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DZBQWSC588;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = QuizCube/Info.plist;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navoriax.QuizCube;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		B62AEC952E5C92FD00CEAAA4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = DZBQWSC588;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B62AEC962E5C92FD00CEAAA4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = DZBQWSC588;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		B62AEC982E5C92FD00CEAAA4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DZBQWSC588;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navoriax.QuizCubeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/QuizCube.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/QuizCube";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		B62AEC992E5C92FD00CEAAA4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DZBQWSC588;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navoriax.QuizCubeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/QuizCube.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/QuizCube";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		B62AEC9B2E5C92FD00CEAAA4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DZBQWSC588;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navoriax.QuizCubeUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = QuizCube;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		B62AEC9C2E5C92FD00CEAAA4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DZBQWSC588;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.navoriax.QuizCubeUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = QuizCube;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B62AEC692E5C92F800CEAAA4 /* Build configuration list for PBXProject "QuizCube" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B62AEC952E5C92FD00CEAAA4 /* Debug */,
				B62AEC962E5C92FD00CEAAA4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B62AEC922E5C92FD00CEAAA4 /* Build configuration list for PBXNativeTarget "QuizCube" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B62AEC932E5C92FD00CEAAA4 /* Debug */,
				B62AEC942E5C92FD00CEAAA4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B62AEC972E5C92FD00CEAAA4 /* Build configuration list for PBXNativeTarget "QuizCubeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B62AEC982E5C92FD00CEAAA4 /* Debug */,
				B62AEC992E5C92FD00CEAAA4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B62AEC9A2E5C92FD00CEAAA4 /* Build configuration list for PBXNativeTarget "QuizCubeUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B62AEC9B2E5C92FD00CEAAA4 /* Debug */,
				B62AEC9C2E5C92FD00CEAAA4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = B62AEC662E5C92F800CEAAA4 /* Project object */;
}
