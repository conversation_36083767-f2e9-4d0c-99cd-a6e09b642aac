//
//  AuthService.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import Foundation
import Combine
import SwiftData

@MainActor
class AuthService: ObservableObject {
    static let shared = AuthService()
    
    @Published var currentUser: User?
    @Published var isLoggedIn: Bool = false
    @Published var isLoading: Bool = false
    
    private let networkService = NetworkService.shared
    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        loadUserFromStorage()
    }
    
    // MARK: - User Storage
    private func loadUserFromStorage() {
        if let userData = userDefaults.data(forKey: "currentUser"),
           let user = try? JSONDecoder().decode(UserResponse.self, from: userData) {
            self.currentUser = User.from(user)
            self.isLoggedIn = user.token != nil
        }
    }
    
    private func saveUserToStorage(_ user: User) {
        let userResponse = UserResponse(
            userId: user.id,
            username: user.username,
            email: user.email,
            avatarUrl: user.avatarUrl,
            token: user.token
        )
        
        if let userData = try? JSONEncoder().encode(userResponse) {
            userDefaults.set(userData, forKey: "currentUser")
        }
    }
    
    private func clearUserFromStorage() {
        userDefaults.removeObject(forKey: "currentUser")
    }
    
    // MARK: - Authentication Methods
    func login(username: String, password: String) -> AnyPublisher<Bool, Never> {
        isLoading = true
        
        let loginData = [
            "username": username,
            "password": password
        ]
        
        guard let body = try? JSONEncoder().encode(loginData) else {
            isLoading = false
            return Just(false).eraseToAnyPublisher()
        }
        
        return networkService.request<UserResponse>(
            endpoint: "/api/auth/login",
            method: .POST,
            body: body
        )
        .map { [weak self] userResponse in
            let user = User.from(userResponse)
            self?.currentUser = user
            self?.isLoggedIn = true
            self?.saveUserToStorage(user)
            return true
        }
        .catch { [weak self] error in
            print("Login error: \(error)")
            self?.isLoggedIn = false
            return Just(false)
        }
        .handleEvents(receiveCompletion: { [weak self] _ in
            self?.isLoading = false
        })
        .eraseToAnyPublisher()
    }
    
    func logout() {
        currentUser?.logout()
        currentUser = nil
        isLoggedIn = false
        clearUserFromStorage()
    }
    
    func refreshToken() -> AnyPublisher<Bool, Never> {
        guard let token = currentUser?.token else {
            return Just(false).eraseToAnyPublisher()
        }
        
        let headers = ["Authorization": "Bearer \(token)"]
        
        return networkService.request<UserResponse>(
            endpoint: "/api/auth/refresh",
            method: .POST,
            headers: headers
        )
        .map { [weak self] userResponse in
            self?.currentUser?.updateToken(userResponse.token)
            if let user = self?.currentUser {
                self?.saveUserToStorage(user)
            }
            return true
        }
        .catch { [weak self] error in
            print("Token refresh error: \(error)")
            // If token refresh fails, logout user
            self?.logout()
            return Just(false)
        }
        .eraseToAnyPublisher()
    }
}
