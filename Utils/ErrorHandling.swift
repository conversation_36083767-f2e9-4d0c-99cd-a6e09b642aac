//
//  ErrorHandling.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

// MARK: - Error Types
enum AppError: Error, LocalizedError {
    case networkError(String)
    case authenticationError
    case dataError(String)
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "网络错误: \(message)"
        case .authenticationError:
            return "认证失败，请重新登录"
        case .dataError(let message):
            return "数据错误: \(message)"
        case .unknownError:
            return "未知错误，请稍后重试"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkError:
            return "请检查网络连接后重试"
        case .authenticationError:
            return "请重新登录"
        case .dataError:
            return "请刷新页面重试"
        case .unknownError:
            return "如果问题持续存在，请联系客服"
        }
    }
}

// MARK: - Error Alert Modifier
struct ErrorAlert: ViewModifier {
    @Binding var error: AppError?
    
    func body(content: Content) -> some View {
        content
            .alert("错误", isPresented: .constant(error != nil)) {
                Button("确定") {
                    error = nil
                }
                
                if case .authenticationError = error {
                    Button("重新登录") {
                        // Handle re-login
                        error = nil
                    }
                }
            } message: {
                if let error = error {
                    VStack(alignment: .leading, spacing: 8) {
                        Text(error.localizedDescription)
                        
                        if let suggestion = error.recoverySuggestion {
                            Text(suggestion)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
    }
}

extension View {
    func errorAlert(_ error: Binding<AppError?>) -> some View {
        modifier(ErrorAlert(error: error))
    }
}

// MARK: - Toast Notification
struct ToastView: View {
    let message: String
    let type: ToastType
    @State private var isVisible = false
    
    enum ToastType {
        case success, error, info
        
        var color: Color {
            switch self {
            case .success: return .green
            case .error: return .red
            case .info: return .blue
            }
        }
        
        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .error: return "xmark.circle.fill"
            case .info: return "info.circle.fill"
            }
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: type.icon)
                .foregroundColor(type.color)
                .font(.title3)
            
            Text(message)
                .font(.body)
                .foregroundColor(.primary)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .scaleEffect(isVisible ? 1 : 0.8)
        .opacity(isVisible ? 1 : 0)
        .onAppear {
            withAnimation(.iOSSpring) {
                isVisible = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                withAnimation(.iOSEaseOut) {
                    isVisible = false
                }
            }
        }
    }
}

// MARK: - Toast Manager
@MainActor
class ToastManager: ObservableObject {
    @Published var toasts: [ToastItem] = []
    
    struct ToastItem: Identifiable {
        let id = UUID()
        let message: String
        let type: ToastView.ToastType
    }
    
    func show(_ message: String, type: ToastView.ToastType = .info) {
        let toast = ToastItem(message: message, type: type)
        toasts.append(toast)
        
        // Auto-remove after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.toasts.removeAll { $0.id == toast.id }
        }
    }
    
    func showSuccess(_ message: String) {
        show(message, type: .success)
    }
    
    func showError(_ message: String) {
        show(message, type: .error)
    }
    
    func showInfo(_ message: String) {
        show(message, type: .info)
    }
}

// MARK: - Toast Overlay Modifier
struct ToastOverlay: ViewModifier {
    @ObservedObject var toastManager: ToastManager
    
    func body(content: Content) -> some View {
        content
            .overlay(
                VStack {
                    Spacer()
                    
                    VStack(spacing: 8) {
                        ForEach(toastManager.toasts) { toast in
                            ToastView(message: toast.message, type: toast.type)
                                .transition(.asymmetric(
                                    insertion: .move(edge: .bottom).combined(with: .opacity),
                                    removal: .move(edge: .bottom).combined(with: .opacity)
                                ))
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.bottom, 100) // Account for tab bar
                }
                .animation(.iOSSpring, value: toastManager.toasts.count)
            )
    }
}

extension View {
    func toastOverlay(_ toastManager: ToastManager) -> some View {
        modifier(ToastOverlay(toastManager: toastManager))
    }
}
