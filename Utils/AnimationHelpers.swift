//
//  AnimationHelpers.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

// MARK: - iOS Native Animation Styles
extension Animation {
    // iOS-style spring animations
    static let iOSSpring = Animation.spring(response: 0.6, dampingFraction: 0.8)
    static let iOSBounce = Animation.spring(response: 0.4, dampingFraction: 0.6)
    static let iOSGentle = Animation.spring(response: 0.8, dampingFraction: 0.9)
    
    // iOS-style easing curves
    static let iOSEaseOut = Animation.timingCurve(0.25, 0.1, 0.25, 1, duration: 0.4)
    static let iOSEaseIn = Animation.timingCurve(0.42, 0, 1, 1, duration: 0.3)
    static let iOSEaseInOut = Animation.timingCurve(0.42, 0, 0.58, 1, duration: 0.5)
    
    // Custom iOS-like animations
    static let cardAppear = Animation.spring(response: 0.7, dampingFraction: 0.8).delay(0.1)
    static let slideReveal = Animation.timingCurve(0.25, 0.46, 0.45, 0.94, duration: 0.6)
    static let scalePress = Animation.spring(response: 0.3, dampingFraction: 0.6)
}

// MARK: - Haptic Feedback Helper
struct HapticManager {
    static func impact(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
    
    static func selection() {
        let generator = UISelectionFeedbackGenerator()
        generator.selectionChanged()
    }
    
    static func notification(_ type: UINotificationFeedbackGenerator.FeedbackType) {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(type)
    }
}

// MARK: - Parallax Effect Modifier
struct ParallaxEffect: ViewModifier {
    let magnitude: CGFloat
    @State private var offset: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .offset(y: offset * magnitude)
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                withAnimation(.linear(duration: 0.1)) {
                    offset = value
                }
            }
    }
}

extension View {
    func parallaxEffect(magnitude: CGFloat = 0.5) -> some View {
        modifier(ParallaxEffect(magnitude: magnitude))
    }
}

// MARK: - Scroll Offset Preference Key
struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Scale on Press Modifier
struct ScaleOnPress: ViewModifier {
    @State private var isPressed = false
    let scale: CGFloat
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? scale : 1.0)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity) { _ in
                withAnimation(.scalePress) {
                    isPressed = true
                }
            } onPressingChanged: { pressing in
                if !pressing {
                    withAnimation(.scalePress) {
                        isPressed = false
                    }
                }
            }
    }
}

extension View {
    func scaleOnPress(scale: CGFloat = 0.95) -> some View {
        modifier(ScaleOnPress(scale: scale))
    }
}

// MARK: - Shimmer Loading Effect
struct ShimmerEffect: ViewModifier {
    @State private var phase: CGFloat = 0
    
    func body(content: Content) -> some View {
        content
            .overlay(
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.clear,
                                Color.white.opacity(0.3),
                                Color.clear
                            ],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .rotationEffect(.degrees(30))
                    .offset(x: phase)
                    .clipped()
            )
            .onAppear {
                withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                    phase = 400
                }
            }
    }
}

extension View {
    func shimmer() -> some View {
        modifier(ShimmerEffect())
    }
}

// MARK: - Bounce Animation Modifier
struct BounceAnimation: ViewModifier {
    @State private var bounceOffset: CGFloat = 0
    let duration: Double
    let amplitude: CGFloat
    
    func body(content: Content) -> some View {
        content
            .offset(y: bounceOffset)
            .onAppear {
                withAnimation(.easeInOut(duration: duration).repeatForever(autoreverses: true)) {
                    bounceOffset = amplitude
                }
            }
    }
}

extension View {
    func bounceAnimation(duration: Double = 2.0, amplitude: CGFloat = 10) -> some View {
        modifier(BounceAnimation(duration: duration, amplitude: amplitude))
    }
}

// MARK: - Slide In Animation
struct SlideInAnimation: ViewModifier {
    let direction: Edge
    let distance: CGFloat
    let delay: Double
    @State private var offset: CGFloat
    
    init(from direction: Edge, distance: CGFloat = 100, delay: Double = 0) {
        self.direction = direction
        self.distance = distance
        self.delay = delay
        
        switch direction {
        case .leading:
            _offset = State(initialValue: -distance)
        case .trailing:
            _offset = State(initialValue: distance)
        case .top:
            _offset = State(initialValue: -distance)
        case .bottom:
            _offset = State(initialValue: distance)
        }
    }
    
    func body(content: Content) -> some View {
        content
            .offset(
                x: direction == .leading || direction == .trailing ? offset : 0,
                y: direction == .top || direction == .bottom ? offset : 0
            )
            .onAppear {
                withAnimation(.iOSSpring.delay(delay)) {
                    offset = 0
                }
            }
    }
}

extension View {
    func slideIn(from direction: Edge, distance: CGFloat = 100, delay: Double = 0) -> some View {
        modifier(SlideInAnimation(from: direction, distance: distance, delay: delay))
    }
}
