//
//  ViewExtensions.swift
//  QuizCube
//
//  Created by <PERSON> on 2025/8/25.
//

import SwiftUI

// MARK: - iOS Native Styling Extensions
extension View {
    // iOS-style card styling
    func iOSCard(cornerRadius: CGFloat = 16, shadowRadius: CGFloat = 8) -> some View {
        self
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(.regularMaterial)
                    .shadow(color: .black.opacity(0.1), radius: shadowRadius, x: 0, y: 4)
            )
    }
    
    // iOS-style button styling
    func iOSButton(
        backgroundColor: Color = .accentColor,
        foregroundColor: Color = .white,
        cornerRadius: CGFloat = 12,
        padding: EdgeInsets = EdgeInsets(top: 12, leading: 24, bottom: 12, trailing: 24)
    ) -> some View {
        self
            .foregroundColor(foregroundColor)
            .padding(padding)
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(backgroundColor)
            )
            .scaleOnPress()
    }
    
    // Glassmorphism effect
    func glassmorphism(
        cornerRadius: CGFloat = 16,
        opacity: Double = 0.8
    ) -> some View {
        self
            .background(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .fill(.ultraThinMaterial.opacity(opacity))
                    .overlay(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(.white.opacity(0.2), lineWidth: 1)
                    )
            )
    }
    
    // Conditional modifier
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
    
    // Safe area padding
    func safeAreaPadding(_ edges: Edge.Set = .all, _ length: CGFloat = 16) -> some View {
        self.padding(edges, length)
    }
    
    // Gradient text
    func gradientText(
        colors: [Color] = [Color(red: 1, green: 0.2, blue: 0.2), Color(red: 0.6, green: 0, blue: 0)],
        startPoint: UnitPoint = .leading,
        endPoint: UnitPoint = .trailing
    ) -> some View {
        self
            .overlay(
                LinearGradient(
                    colors: colors,
                    startPoint: startPoint,
                    endPoint: endPoint
                )
                .mask(self)
            )
            .foregroundColor(.clear)
    }
    
    // Skeleton loading state
    func skeleton(isLoading: Bool, cornerRadius: CGFloat = 8) -> some View {
        self
            .opacity(isLoading ? 0 : 1)
            .overlay(
                Group {
                    if isLoading {
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(Color.gray.opacity(0.3))
                            .shimmer()
                    }
                }
            )
    }
    
    // Pulse animation
    func pulse(duration: Double = 1.0, minOpacity: Double = 0.5) -> some View {
        self
            .opacity(minOpacity)
            .animation(
                .easeInOut(duration: duration)
                .repeatForever(autoreverses: true),
                value: minOpacity
            )
    }
}

// MARK: - Color Extensions
extension Color {
    // App-specific colors
    static let primaryRed = Color(red: 0.67, green: 0, blue: 0)
    static let secondaryRed = Color(red: 1, green: 0.2, blue: 0.2)
    static let darkBackground = Color(red: 0.08, green: 0.08, blue: 0.08)
    static let cardBackground = Color.black.opacity(0.6)
    
    // Dynamic colors that adapt to light/dark mode
    static let adaptiveBackground = Color(UIColor.systemBackground)
    static let adaptiveSecondaryBackground = Color(UIColor.secondarySystemBackground)
    static let adaptiveText = Color(UIColor.label)
    static let adaptiveSecondaryText = Color(UIColor.secondaryLabel)
    
    // Gradient helpers
    static func redGradient() -> LinearGradient {
        LinearGradient(
            colors: [secondaryRed, primaryRed],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    static func darkGradient() -> LinearGradient {
        LinearGradient(
            colors: [
                Color.black,
                Color(red: 0.08, green: 0.08, blue: 0.08),
                Color(red: 0.2, green: 0, blue: 0)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

// MARK: - Font Extensions
extension Font {
    // App-specific fonts
    static let appTitle = Font.largeTitle.weight(.black)
    static let appHeadline = Font.headline.weight(.bold)
    static let appBody = Font.body.weight(.medium)
    static let appCaption = Font.caption.weight(.medium)
    
    // Dynamic font sizes
    static func appCustom(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        return .system(size: size, weight: weight, design: .default)
    }
}

// MARK: - Text Extensions
extension Text {
    // Styled text helpers
    func appTitle() -> some View {
        self
            .font(.appTitle)
            .foregroundColor(.white)
    }
    
    func appHeadline() -> some View {
        self
            .font(.appHeadline)
            .foregroundColor(.white)
    }
    
    func appBody() -> some View {
        self
            .font(.appBody)
            .foregroundColor(.white.opacity(0.9))
    }
    
    func appCaption() -> some View {
        self
            .font(.appCaption)
            .foregroundColor(.white.opacity(0.7))
    }
    
    func gradientTitle() -> some View {
        self
            .font(.appTitle)
            .gradientText()
    }
}

// MARK: - Geometry Extensions
extension CGSize {
    static let screenSize = UIScreen.main.bounds.size
    
    var aspectRatio: CGFloat {
        return width / height
    }
}

extension CGFloat {
    static let screenWidth = UIScreen.main.bounds.width
    static let screenHeight = UIScreen.main.bounds.height
    
    // Common spacing values
    static let spacing4: CGFloat = 4
    static let spacing8: CGFloat = 8
    static let spacing12: CGFloat = 12
    static let spacing16: CGFloat = 16
    static let spacing20: CGFloat = 20
    static let spacing24: CGFloat = 24
    static let spacing32: CGFloat = 32
}

// MARK: - EdgeInsets Extensions
extension EdgeInsets {
    static let zero = EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0)
    static let small = EdgeInsets(top: 8, leading: 8, bottom: 8, trailing: 8)
    static let medium = EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
    static let large = EdgeInsets(top: 24, leading: 24, bottom: 24, trailing: 24)
    
    init(all value: CGFloat) {
        self.init(top: value, leading: value, bottom: value, trailing: value)
    }
    
    init(horizontal: CGFloat = 0, vertical: CGFloat = 0) {
        self.init(top: vertical, leading: horizontal, bottom: vertical, trailing: horizontal)
    }
}
